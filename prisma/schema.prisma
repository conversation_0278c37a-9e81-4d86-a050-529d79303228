// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Article {
  id        String   @id @default(cuid())
  slug      String   @unique
  title     String
  excerpt   String?
  contentJson String  // TipTap JSON content
  status    ArticleStatus @default(DRAFT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("articles")
}

enum ArticleStatus {
  DRAFT
  PUBLISHED
}
