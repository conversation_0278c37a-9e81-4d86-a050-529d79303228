# WeChat工作流优先修复方案

## 🚨 立即修复的关键问题

### 1. 预览功能修复（最高优先级）

**问题**：SimpleEditor预览模式只显示纯文本，无法渲染WeChat组件

**解决方案**：创建ArticlePreview组件

```typescript
// src/components/editor/ArticlePreview.tsx
'use client';

import React from 'react';
import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '@/app/posts/components';

interface ArticlePreviewProps {
  content: any; // TipTap JSON
  title?: string;
}

export function ArticlePreview({ content, title }: ArticlePreviewProps) {
  const renderNode = (node: any, index: number): React.ReactNode => {
    switch (node.type) {
      case 'paragraph':
        return (
          <p key={index}>
            {node.content?.map((textNode: any, i: number) => (
              <span key={i}>{textNode.text}</span>
            ))}
          </p>
        );
      
      case 'heading':
        const HeadingTag = `h${node.attrs.level}` as keyof JSX.IntrinsicElements;
        return (
          <HeadingTag key={index}>
            {node.content?.map((textNode: any, i: number) => (
              <span key={i}>{textNode.text}</span>
            ))}
          </HeadingTag>
        );
      
      case 'image':
        return (
          <ImageContainer
            key={index}
            src={node.attrs.src}
            alt={node.attrs.alt || ''}
            caption={node.attrs.caption}
          />
        );
      
      case 'codeBlock':
        return (
          <CodeBlock key={index} language={node.attrs.language}>
            {node.content?.map((textNode: any) => textNode.text).join('')}
          </CodeBlock>
        );
      
      case 'horizontalRule':
        return <Divider key={index} />;
      
      default:
        return null;
    }
  };

  return (
    <div className="article-preview">
      {/* 应用完整的WeChat样式 */}
      <div className="article-container wechat-article-body">
        {title && <h1 className="article-title">{title}</h1>}
        <div className="article-content">
          {content?.content?.map((node: any, index: number) => 
            renderNode(node, index)
          )}
        </div>
      </div>
    </div>
  );
}
```

### 2. 动态路由修复

**问题**：`/posts/[slug]/page.tsx` 未集成数据库文章

**解决方案**：更新动态路由支持数据库文章

```typescript
// src/app/posts/[slug]/page.tsx
import { articleService } from '@/lib/services/article-service';
import { getPostBySlug } from '@/lib/posts';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { ArticlePreview } from '@/components/editor/ArticlePreview';
import { notFound } from 'next/navigation';

export default async function PostPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  
  // 首先尝试从数据库获取文章
  let article = await articleService.getArticleBySlug(slug);
  
  // 如果数据库中没有，尝试从文件系统获取（向后兼容）
  if (!article) {
    const filePost = getPostBySlug(slug);
    if (!filePost) {
      return notFound();
    }
    
    // 渲染文件系统文章（保持原有逻辑）
    const metadata: ArticleMetadata = {
      title: filePost.title,
      date: filePost.date,
      excerpt: filePost.excerpt
    };

    const htmlContent = marked(filePost.content);
    return (
      <ArticleLayout metadata={metadata}>
        <div
          className="article-content"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </ArticleLayout>
    );
  }

  // 渲染数据库文章
  const metadata: ArticleMetadata = {
    title: article.title,
    date: article.createdAt,
    excerpt: article.excerpt || undefined
  };

  const content = JSON.parse(article.contentJson);

  return (
    <ArticleLayout metadata={metadata}>
      <ArticlePreview content={content} />
    </ArticleLayout>
  );
}
```

### 3. 编辑器预览集成

**问题**：新建文章页面预览功能不完整

**解决方案**：更新SimpleEditor预览模式

```typescript
// 更新 src/app/admin/articles/new/page.tsx 的预览部分
{isPreview ? (
  <div className="prose prose-lg max-w-none prose-invert">
    <ArticlePreview 
      content={content} 
      title={formData.title || '文章标题'} 
    />
  </div>
) : (
  <SimpleEditor
    content={content}
    onChange={setContent}
  />
)}
```

## 🔧 中优先级修复

### 4. 内容转换API完善

**问题**：`/api/convert/content` 功能不完整

**解决方案**：实现完整的转换API

```typescript
// src/app/api/convert/content/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { ContentConverter } from '@/lib/converters/content-converter';

export async function POST(request: NextRequest) {
  try {
    const { content, type, options } = await request.json();
    
    const converter = new ContentConverter();
    const result = await converter.convert(content, type, options);
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '内容转换失败',
      message: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}
```

### 5. TipTap编辑器增强

**问题**：WeChatEditor缺少WeChat组件扩展

**解决方案**：创建自定义TipTap扩展

```typescript
// src/components/editor/extensions/ImageContainerExtension.ts
import { Node } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { ImageContainerNodeView } from './ImageContainerNodeView';

export const ImageContainerExtension = Node.create({
  name: 'imageContainer',
  
  group: 'block',
  
  atom: true,
  
  addAttributes() {
    return {
      src: { default: null },
      alt: { default: null },
      caption: { default: null },
      width: { default: '100%' },
      height: { default: 'auto' },
    };
  },
  
  parseHTML() {
    return [{ tag: 'image-container' }];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['image-container', HTMLAttributes];
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(ImageContainerNodeView);
  },
});
```

## 📋 实施计划

### 第一阶段（今天完成）
1. ✅ 创建ArticlePreview组件
2. ✅ 修复动态路由
3. ✅ 更新新建文章页面预览

### 第二阶段（本周内）
1. 完善内容转换API
2. 实现TipTap自定义扩展
3. 优化管理界面功能

### 第三阶段（下周）
1. 添加批量操作功能
2. 实现搜索和过滤
3. 优化性能和用户体验

## 🧪 测试计划

每个修复完成后需要测试：
1. 创建新文章并预览
2. 访问动态路由文章
3. 复制功能是否正常
4. 样式是否一致
5. 移动端兼容性

## 📞 需要确认的问题

1. **数据库选择**：是否需要从SQLite迁移到PostgreSQL？
2. **现有文章**：如何处理现有的文件系统文章？
3. **图片上传**：是否需要实现图片上传功能？
4. **权限控制**：是否需要添加用户认证？

请确认这些问题，我将据此调整实施计划。
