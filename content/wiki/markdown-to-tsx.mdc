---
description:
globs:
alwaysApply: false
---
## Rule: Convert Markdown to TSX Page Component

This rule outlines the transformation of a Markdown file into a TSX page component, specifically for a Next.js blog structure.

**Target File Structure (TSX):**

```typescript
'use client';

import React from 'react';
import { ArticleLayout } from '@/components/layout'; // Assuming this path
import { PostData } from '@/lib/posts'; // Assuming this path

export default function [PageName]() {
  const post: PostData = {
    slug: '[markdown_slug]', // e.g., '2025-05-25' or 'YYYY-MM-DD/post-name'
    title: '[markdown_h1_title]',
    date: '[markdown_file_date]', // Extracted from filename or frontmatter
    content: `
      <!-- Converted HTML from Markdown below -->
      [html_content]
    `
  };

  return <ArticleLayout post={post} />;
}
```

**Conversion Steps & Patterns:**

1.  **Extract Metadata:**
    *   `[PageName]`: Derive from Markdown filename (e.g., `MyPostPage` from `my-post.md`).
    *   `[markdown_slug]`: Derive from directory structure/filename (e.g., `2025-05-25` or `2025-05-25/my-post-slug`).
    *   `[markdown_h1_title]`: Use the first H1 heading (`# Title`) from Markdown.
    *   `[markdown_file_date]`: Extract from the filename pattern (e.g., `YYYY-MM-DD` from `content/YYYY-MM-DD/filename.md`) or from Markdown frontmatter if available.

2.  **Content Conversion (`[html_content]`):**

    *   **General Wrapper**: The entire Markdown body is converted to HTML and placed within the `content` backticks.

    *   **Headings**:
        *   `# Title` (first H1): Used for `post.title`. Subsequent H1s could become `<h1>`.
        *   `## Subtitle`: Convert to `<div class="section"><h2>Subtitle</h2></div>`.
        *   `### Sub-subtitle`: Convert to `<div class="section"><h3>Sub-subtitle</h3></div>`.

    *   **Paragraphs**:
        *   Regular paragraphs: `<p>Text</p>`.
        *   *Special Case (Intro)*: If the first paragraph is intended as an introduction, wrap it in `<div class="intro-text">...</div>`. (This might require manual identification or a specific Markdown marker).

    *   **Lists**:
        *   Unordered (`- item` or `* item`):
            ```html
            <ul>
              <li>item</li>
            </ul>
            ```
        *   Ordered (`1. item`):
            ```html
            <ol>
              <li>item</li>
            </ol>
            ```
        *   Nested lists should be handled accordingly.
        *   Line breaks (`<br />`) inside list items seem to be manually preserved from the example.

    *   **Links**:
        *   `[text](url)`: `<a href="url">text</a>`.

    *   **Images**:
        *   `![alt text](path/to/image.png)`:
            ```html
            <div class="image-container">
              <img src="/posts/[markdown_file_date_based_path]/img/image.png" alt="alt text" width="800" height="450" style="width: 100%; height: auto;" />
            </div>
            ```
            *   The `src` path needs to be constructed. For example, if Markdown image is `img/image.png` and the post date is `2025-05-25`, the path becomes `/posts/2025-05-25/img/image.png`.

    *   **Horizontal Rules**:
        *   `---`: `<hr class="divider" />`.

    *   **Code Blocks**:
        *   \`\`\`language\ncode\n\`\`\`:
            ```html
            <pre><code class="language-language">{\`code\`}</code></pre>
            ```
            *   Note: Backticks and other special characters within the code block in the final HTML string might need escaping (e.g. `{\`code\`}` if it's part of a template literal in TSX). The example shows direct embedding.
            *   JSON example: `<pre><code>{\n  "key": "value"\n}</code></pre>`

    *   **Blockquotes**:
        *   `> quote`: `<blockquote><p>quote</p></blockquote>`
        *   *Special Case (Highlight Box)*: `> **重要提示**：text` becomes:
            ```html
            <div class="highlight-box">
              <strong>重要提示</strong>：text
            </div>
            ```

    *   **Inline Styling**:
        *   `**bold**` or `__bold__`: `<strong>bold</strong>`
        *   `*italic*` or `_italic_`: `<em>italic</em>`
        *   \`inline code\`: `<code>inline code</code>`

    *   **HTML Escaping**: Ensure proper HTML escaping for content like `<`, `>`, `&`, etc., where necessary, especially within code blocks or if the Markdown might contain raw HTML.

**Placeholders to be filled by Cursor (or a script):**

*   `[PageName]`: e.g., `CursorRequestOptimizationPage`
*   `[markdown_slug]`: e.g., `2025-05-25`
*   `[markdown_h1_title]`: e.g., `别抠搜 Cursor 的对话请求了，这个 MCP/Rules 让你告别请求数焦虑`
*   `[markdown_file_date]`: e.g., `2025-05-25`
*   `[html_content]`: The fully converted HTML string.
*   `[markdown_file_date_based_path]`: Path segment based on date, e.g., `2025-05-25`.

**Example Invocation (Conceptual):**

If Cursor were to use this rule, it might look for a Markdown file, parse it according to these patterns, and then construct the TSX file.

**Assumptions & Considerations:**

*   The paths to `ArticleLayout` and `PostData` are fixed or configurable.
*   Date extraction logic (from filename or frontmatter) needs to be robust.
*   Image path transformation requires knowing the base Markdown image directory and the target public directory structure.
*   The "intro-text" and "highlight-box" are custom conventions. Standard Markdown blockquotes are `<blockquote>`.
*   This rule describes a one-way conversion. Changes in the TSX might not be easily convertible back to Markdown.
*   Mermaid diagram conversion (seen in the other `.tsx` file) is a more advanced feature not covered by this basic Markdown-to-HTML conversion. It would require specific handling for `lang="mermaid"` code blocks and client-side JavaScript execution.

