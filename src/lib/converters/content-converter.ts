import { ConvertContentRequest, ConvertContentResponse } from '@/types/api';

export class ContentConverter {
  async convert(
    content: string, 
    type: 'html' | 'markdown', 
    options?: {
      preserveFormatting?: boolean;
      autoDetectComponents?: boolean;
    }
  ): Promise<ConvertContentResponse> {
    const { preserveFormatting = true, autoDetectComponents = true } = options || {};
    
    let tiptapJson: any;
    const suggestions: string[] = [];
    const componentUsage = {
      imageContainer: 0,
      highlightBox: 0,
      codeBlock: 0,
      section: 0,
      divider: 0,
    };

    if (type === 'html') {
      tiptapJson = this.convertHtmlToTipTap(content, autoDetectComponents, componentUsage, suggestions);
    } else {
      tiptapJson = this.convertMarkdownToTipTap(content, autoDetectComponents, componentUsage, suggestions);
    }

    return {
      tiptapJson,
      suggestions,
      componentUsage,
    };
  }

  private convertHtmlToTipTap(
    html: string, 
    autoDetectComponents: boolean,
    componentUsage: any,
    suggestions: string[]
  ): any {
    // 基础的HTML到TipTap转换
    const doc = {
      type: 'doc',
      content: [],
    };

    // 简单的HTML解析和转换
    const lines = html.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (!trimmedLine) continue;

      // 检测图片
      if (trimmedLine.includes('<img')) {
        const srcMatch = trimmedLine.match(/src="([^"]+)"/);
        const altMatch = trimmedLine.match(/alt="([^"]+)"/);
        
        if (srcMatch) {
          doc.content.push({
            type: 'image',
            attrs: {
              src: srcMatch[1],
              alt: altMatch ? altMatch[1] : '',
            },
          });
          componentUsage.imageContainer++;
          
          if (autoDetectComponents) {
            suggestions.push('建议使用 ImageContainer 组件优化图片显示');
          }
        }
        continue;
      }

      // 检测标题
      if (trimmedLine.startsWith('<h')) {
        const levelMatch = trimmedLine.match(/<h([1-6])/);
        const textMatch = trimmedLine.match(/>([^<]+)</);
        
        if (levelMatch && textMatch) {
          doc.content.push({
            type: 'heading',
            attrs: { level: parseInt(levelMatch[1]) },
            content: [{ type: 'text', text: textMatch[1] }],
          });
        }
        continue;
      }

      // 检测代码块
      if (trimmedLine.includes('<pre') || trimmedLine.includes('<code')) {
        const codeMatch = trimmedLine.match(/>([^<]+)</);
        if (codeMatch) {
          doc.content.push({
            type: 'codeBlock',
            content: [{ type: 'text', text: codeMatch[1] }],
          });
          componentUsage.codeBlock++;
          
          if (autoDetectComponents) {
            suggestions.push('建议使用 CodeBlock 组件提供语法高亮');
          }
        }
        continue;
      }

      // 检测强调内容（可能转换为HighlightBox）
      if (trimmedLine.includes('<strong') || trimmedLine.includes('<em')) {
        const textMatch = trimmedLine.match(/>([^<]+)</);
        if (textMatch && autoDetectComponents) {
          suggestions.push('检测到强调内容，建议使用 HighlightBox 组件突出显示');
          componentUsage.highlightBox++;
        }
      }

      // 检测分割线
      if (trimmedLine.includes('<hr') || trimmedLine === '---') {
        doc.content.push({
          type: 'horizontalRule',
        });
        componentUsage.divider++;
        
        if (autoDetectComponents) {
          suggestions.push('建议使用 Divider 组件创建更美观的分割线');
        }
        continue;
      }

      // 默认段落处理
      const cleanText = trimmedLine.replace(/<[^>]+>/g, '').trim();
      if (cleanText) {
        doc.content.push({
          type: 'paragraph',
          content: [{ type: 'text', text: cleanText }],
        });
      }
    }

    return doc;
  }

  private convertMarkdownToTipTap(
    markdown: string,
    autoDetectComponents: boolean,
    componentUsage: any,
    suggestions: string[]
  ): any {
    const doc = {
      type: 'doc',
      content: [],
    };

    const lines = markdown.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (!trimmedLine) continue;

      // 检测标题
      if (trimmedLine.startsWith('#')) {
        const level = trimmedLine.match(/^#+/)?.[0].length || 1;
        const text = trimmedLine.replace(/^#+\s*/, '');
        
        doc.content.push({
          type: 'heading',
          attrs: { level: Math.min(level, 6) },
          content: [{ type: 'text', text }],
        });
        continue;
      }

      // 检测图片
      if (trimmedLine.includes('![')) {
        const match = trimmedLine.match(/!\[([^\]]*)\]\(([^)]+)\)/);
        if (match) {
          doc.content.push({
            type: 'image',
            attrs: {
              src: match[2],
              alt: match[1],
            },
          });
          componentUsage.imageContainer++;
          
          if (autoDetectComponents) {
            suggestions.push('建议使用 ImageContainer 组件优化图片显示');
          }
        }
        continue;
      }

      // 检测代码块
      if (trimmedLine.startsWith('```')) {
        componentUsage.codeBlock++;
        if (autoDetectComponents) {
          suggestions.push('建议使用 CodeBlock 组件提供语法高亮');
        }
        continue;
      }

      // 检测分割线
      if (trimmedLine === '---' || trimmedLine === '***') {
        doc.content.push({
          type: 'horizontalRule',
        });
        componentUsage.divider++;
        
        if (autoDetectComponents) {
          suggestions.push('建议使用 Divider 组件创建更美观的分割线');
        }
        continue;
      }

      // 检测引用（可能转换为HighlightBox）
      if (trimmedLine.startsWith('>')) {
        const text = trimmedLine.replace(/^>\s*/, '');
        if (autoDetectComponents) {
          suggestions.push('检测到引用内容，建议使用 HighlightBox 组件突出显示');
          componentUsage.highlightBox++;
        }
        
        doc.content.push({
          type: 'paragraph',
          content: [{ type: 'text', text }],
        });
        continue;
      }

      // 默认段落
      doc.content.push({
        type: 'paragraph',
        content: [{ type: 'text', text: trimmedLine }],
      });
    }

    return doc;
  }
}
