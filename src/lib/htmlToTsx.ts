/**
 * HTML 到 React TSX 转换工具
 * 支持属性名转换、自闭合标签、内联样式对象化等
 */

// HTML 属性名到 React 属性名的映射
const HTML_TO_REACT_ATTRS: Record<string, string> = {
  'class': 'className',
  'for': 'htmlFor',
  'tabindex': 'tabIndex',
  'readonly': 'readOnly',
  'maxlength': 'maxLength',
  'cellpadding': 'cellPadding',
  'cellspacing': 'cellSpacing',
  'rowspan': 'rowSpan',
  'colspan': 'colSpan',
  'usemap': 'useMap',
  'frameborder': 'frameBorder',
  'contenteditable': 'contentEditable',
  'crossorigin': 'crossOrigin',
  'datetime': 'dateTime',
  'enctype': 'encType',
  'formaction': 'formAction',
  'formenctype': 'formEncType',
  'formmethod': 'formMethod',
  'formnovalidate': 'formNoValidate',
  'formtarget': 'formTarget',
  'hreflang': 'hrefLang',
  'inputmode': 'inputMode',
  'novalidate': 'noValidate',
  'radiogroup': 'radioGroup',
  'spellcheck': 'spellCheck',
  'srcdoc': 'srcDoc',
  'srclang': 'srcLang',
  'srcset': 'srcSet',
  'autofocus': 'autoFocus',
  'autoplay': 'autoPlay',
  'controls': 'controls',
  'defer': 'defer',
  'disabled': 'disabled',
  'hidden': 'hidden',
  'loop': 'loop',
  'multiple': 'multiple',
  'muted': 'muted',
  'open': 'open',
  'required': 'required',
  'reversed': 'reversed',
  'selected': 'selected',
  'autoComplete': 'autoComplete',
  'capture': 'capture',
  'challenge': 'challenge',
  'charset': 'charSet',
  'cite': 'cite',
  'classname': 'className',
  'cols': 'cols',
  'content': 'content',
  'contextmenu': 'contextMenu',
  'coords': 'coords',
  'data': 'data',
  'default': 'default',
  'dir': 'dir',
  'dirname': 'dirName',
  'download': 'download',
  'draggable': 'draggable',
  'dropzone': 'dropzone',
  'form': 'form',
  'high': 'high',
  'href': 'href',
  'htmlfor': 'htmlFor',
  'httpequiv': 'httpEquiv',
  'icon': 'icon',
  'id': 'id',
  'is': 'is',
  'itemid': 'itemID',
  'itemprop': 'itemProp',
  'itemref': 'itemRef',
  'itemscope': 'itemScope',
  'itemtype': 'itemType',
  'kind': 'kind',
  'label': 'label',
  'lang': 'lang',
  'list': 'list',
  'low': 'low',
  'manifest': 'manifest',
  'max': 'max',
  'media': 'media',
  'mediagroup': 'mediaGroup',
  'method': 'method',
  'min': 'min',
  'minlength': 'minLength',
  'name': 'name',
  'nonce': 'nonce',
  'optimum': 'optimum',
  'pattern': 'pattern',
  'placeholder': 'placeholder',
  'poster': 'poster',
  'preload': 'preload',
  'rel': 'rel',
  'role': 'role',
  'rows': 'rows',
  'sandbox': 'sandbox',
  'scope': 'scope',
  'scoped': 'scoped',
  'scrolling': 'scrolling',
  'security': 'security',
  'shape': 'shape',
  'size': 'size',
  'sizes': 'sizes',
  'span': 'span',
  'src': 'src',
  'start': 'start',
  'step': 'step',
  'style': 'style',
  'summary': 'summary',
  'target': 'target',
  'title': 'title',
  'type': 'type',
  'value': 'value',
  'width': 'width',
  'wmode': 'wmode',
  'wrap': 'wrap'
};

// 事件处理器映射
const HTML_TO_REACT_EVENTS: Record<string, string> = {
  'onclick': 'onClick',
  'onchange': 'onChange',
  'onsubmit': 'onSubmit',
  'onload': 'onLoad',
  'onunload': 'onUnload',
  'onfocus': 'onFocus',
  'onblur': 'onBlur',
  'onmouseover': 'onMouseOver',
  'onmouseout': 'onMouseOut',
  'onmousedown': 'onMouseDown',
  'onmouseup': 'onMouseUp',
  'onmousemove': 'onMouseMove',
  'onkeydown': 'onKeyDown',
  'onkeyup': 'onKeyUp',
  'onkeypress': 'onKeyPress',
  'onresize': 'onResize',
  'onscroll': 'onScroll',
  'onerror': 'onError',
  'onabort': 'onAbort',
  'oncanplay': 'onCanPlay',
  'oncanplaythrough': 'onCanPlayThrough',
  'ondurationchange': 'onDurationChange',
  'onemptied': 'onEmptied',
  'onended': 'onEnded',
  'onloadeddata': 'onLoadedData',
  'onloadedmetadata': 'onLoadedMetadata',
  'onloadstart': 'onLoadStart',
  'onpause': 'onPause',
  'onplay': 'onPlay',
  'onplaying': 'onPlaying',
  'onprogress': 'onProgress',
  'onratechange': 'onRateChange',
  'onseeked': 'onSeeked',
  'onseeking': 'onSeeking',
  'onstalled': 'onStalled',
  'onsuspend': 'onSuspend',
  'ontimeupdate': 'onTimeUpdate',
  'onvolumechange': 'onVolumeChange',
  'onwaiting': 'onWaiting'
};

// 自闭合标签列表
const SELF_CLOSING_TAGS = new Set([
  'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
  'link', 'meta', 'param', 'source', 'track', 'wbr'
]);

/**
 * 将 CSS 样式字符串转换为 React 样式对象
 */
function parseStyleString(styleStr: string): string {
  if (!styleStr.trim()) return '{}';
  
  const styles: Record<string, string> = {};
  const declarations = styleStr.split(';').filter(decl => decl.trim());
  
  declarations.forEach(decl => {
    const [property, value] = decl.split(':').map(s => s.trim());
    if (property && value) {
      // 将 kebab-case 转换为 camelCase
      const camelProperty = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      styles[camelProperty] = value;
    }
  });
  
  return JSON.stringify(styles, null, 2);
}

/**
 * 转换 HTML 属性为 React 属性
 */
function convertAttributes(html: string): string {
  // 转换属性名
  let result = html;
  
  // 处理 style 属性
  result = result.replace(/style="([^"]*)"/g, (match, styleValue) => {
    const styleObject = parseStyleString(styleValue);
    return `style={${styleObject}}`;
  });
  
  // 处理其他属性
  Object.entries(HTML_TO_REACT_ATTRS).forEach(([htmlAttr, reactAttr]) => {
    const regex = new RegExp(`\\b${htmlAttr}=`, 'gi');
    result = result.replace(regex, `${reactAttr}=`);
  });
  
  // 处理事件处理器
  Object.entries(HTML_TO_REACT_EVENTS).forEach(([htmlEvent, reactEvent]) => {
    const regex = new RegExp(`\\b${htmlEvent}=`, 'gi');
    result = result.replace(regex, `${reactEvent}=`);
  });
  
  // 处理布尔属性（如 disabled, checked 等）
  result = result.replace(/\b(disabled|checked|selected|multiple|readonly|required|autofocus|autoplay|controls|defer|hidden|loop|muted|open|reversed)\b(?!=)/g, '{true}');
  
  return result;
}

/**
 * 转换自闭合标签
 */
function convertSelfClosingTags(html: string): string {
  let result = html;
  
  SELF_CLOSING_TAGS.forEach(tag => {
    result = result.replace(new RegExp(`<${tag}([^>]*?)>`, 'gi'), (match, attributes) => {
      // 如果已自闭合则返回原匹配
      if (match.endsWith('/>')) return match;
      // 添加自闭合斜杠
      return `<${tag}${attributes.trim()} />`;
    });
  });
  
  return result;
}

/**
 * 主转换函数
 */
export function convertHtmlToTsx(html: string): string {
  if (!html.trim()) return '';
  
  let result = html;
  
  // 1. 转换属性
  result = convertAttributes(result);
  
  // 2. 转换自闭合标签
  result = convertSelfClosingTags(result);
  
  // 3. 处理注释（HTML 注释转换为 JSX 注释）
  result = result.replace(/<!--(.*?)-->/g, '{/* $1 */}');
  
  // 4. 格式化（基本的缩进处理）
  result = formatTsx(result);
  
  return result;
}

/**
 * 基本的 TSX 格式化
 */
function formatTsx(tsx: string): string {
  // 这里可以添加更复杂的格式化逻辑
  // 目前只做基本的换行和缩进处理
  return tsx
    .replace(/></g, '>\n<')
    .replace(/^\s+|\s+$/gm, '') // 移除每行首尾空格
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join('\n');
}

/**
 * 验证转换结果是否为有效的 TSX
 */
export function validateTsx(tsx: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 基本的语法检查
  const openTags = tsx.match(/<[^/][^>]*>/g) || [];
  const closeTags = tsx.match(/<\/[^>]+>/g) || [];
  
  // 检查标签是否匹配（简单检查）
  const openTagNames = openTags.map(tag => tag.match(/<(\w+)/)?.[1]).filter(Boolean);
  const closeTagNames = closeTags.map(tag => tag.match(/<\/(\w+)/)?.[1]).filter(Boolean);
  
  // 移除自闭合标签后检查开闭标签是否匹配
  const filteredOpenTags = openTagNames.filter(tag => !SELF_CLOSING_TAGS.has(tag || ''));
  
  if (filteredOpenTags.length !== closeTagNames.length) {
    errors.push('标签数量不匹配：开标签和闭标签数量不一致');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
