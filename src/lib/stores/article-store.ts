import { create } from 'zustand';
import { Article, CreateArticleRequest, UpdateArticleRequest } from '@/types/api';

interface ArticleState {
  // 状态
  articles: Article[];
  currentArticle: Article | null;
  loading: boolean;
  error: string | null;
  
  // 分页
  currentPage: number;
  totalPages: number;
  total: number;
  
  // 筛选
  searchTerm: string;
  statusFilter: 'ALL' | 'DRAFT' | 'PUBLISHED';
  
  // Actions
  setArticles: (articles: Article[]) => void;
  setCurrentArticle: (article: Article | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 分页 Actions
  setCurrentPage: (page: number) => void;
  setPagination: (total: number, totalPages: number) => void;
  
  // 筛选 Actions
  setSearchTerm: (term: string) => void;
  setStatusFilter: (status: 'ALL' | 'DRAFT' | 'PUBLISHED') => void;
  
  // API Actions
  fetchArticles: () => Promise<void>;
  fetchArticleById: (id: string) => Promise<void>;
  createArticle: (data: CreateArticleRequest) => Promise<Article>;
  updateArticle: (id: string, data: UpdateArticleRequest) => Promise<Article>;
  deleteArticle: (id: string) => Promise<void>;
}

export const useArticleStore = create<ArticleState>((set, get) => ({
  // 初始状态
  articles: [],
  currentArticle: null,
  loading: false,
  error: null,
  
  currentPage: 1,
  totalPages: 0,
  total: 0,
  
  searchTerm: '',
  statusFilter: 'ALL',
  
  // 基础 Actions
  setArticles: (articles) => set({ articles }),
  setCurrentArticle: (article) => set({ currentArticle: article }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  
  // 分页 Actions
  setCurrentPage: (page) => set({ currentPage: page }),
  setPagination: (total, totalPages) => set({ total, totalPages }),
  
  // 筛选 Actions
  setSearchTerm: (term) => set({ searchTerm: term }),
  setStatusFilter: (status) => set({ statusFilter: status }),
  
  // API Actions
  fetchArticles: async () => {
    const { currentPage, searchTerm, statusFilter } = get();
    set({ loading: true, error: null });
    
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        status: statusFilter,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      });
      
      const response = await fetch(`/api/articles?${params}`);
      const result = await response.json();
      
      if (result.success) {
        set({
          articles: result.data.items,
          total: result.data.total,
          totalPages: result.data.totalPages,
          loading: false,
        });
      } else {
        set({ error: result.error || '获取文章失败', loading: false });
      }
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '网络错误', 
        loading: false 
      });
    }
  },
  
  fetchArticleById: async (id: string) => {
    set({ loading: true, error: null });
    
    try {
      const response = await fetch(`/api/articles/${id}`);
      const result = await response.json();
      
      if (result.success) {
        set({ currentArticle: result.data, loading: false });
      } else {
        set({ error: result.error || '获取文章失败', loading: false });
      }
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '网络错误', 
        loading: false 
      });
    }
  },
  
  createArticle: async (data: CreateArticleRequest) => {
    set({ loading: true, error: null });
    
    try {
      const response = await fetch('/api/articles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (result.success) {
        set({ loading: false });
        // 刷新文章列表
        get().fetchArticles();
        return result.data;
      } else {
        set({ error: result.error || '创建文章失败', loading: false });
        throw new Error(result.error || '创建文章失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  updateArticle: async (id: string, data: UpdateArticleRequest) => {
    set({ loading: true, error: null });
    
    try {
      const response = await fetch(`/api/articles/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (result.success) {
        set({ 
          currentArticle: result.data,
          loading: false 
        });
        // 刷新文章列表
        get().fetchArticles();
        return result.data;
      } else {
        set({ error: result.error || '更新文章失败', loading: false });
        throw new Error(result.error || '更新文章失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  deleteArticle: async (id: string) => {
    set({ loading: true, error: null });
    
    try {
      const response = await fetch(`/api/articles/${id}`, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      
      if (result.success) {
        set({ loading: false });
        // 刷新文章列表
        get().fetchArticles();
      } else {
        set({ error: result.error || '删除文章失败', loading: false });
        throw new Error(result.error || '删除文章失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
}));
