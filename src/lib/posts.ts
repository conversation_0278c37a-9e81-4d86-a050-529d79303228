import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

export interface PostData {
  slug: string;
  title: string;
  date: string;
  content: string;
  excerpt?: string;
  coverImage?: string;
  [key: string]: string | undefined;
}

const contentDirectory = path.join(process.cwd(), 'content');

export function getPostSlugs(): string[] {
  try {
    const items = fs.readdirSync(contentDirectory);
    return items.filter(item => {
      const itemPath = path.join(contentDirectory, item);
      return fs.statSync(itemPath).isDirectory() && /^\d{4}-\d{2}-\d{2}$/.test(item) && !item.startsWith('.');
    });
  } catch (error) {
    console.error('Error reading content directory for slugs:', error);
    return [];
  }
}

// 处理文章内容中的图片路径
function processImagePaths(content: string, dateSlug: string): string {
  // 替换相对路径的图片引用为绝对路径
  // 例如: ![alt](img/image.png) -> ![alt](/posts/2025-05-25/img/image.png)
  return content.replace(
    /!\[(.*?)\]\((img\/.*?)\)/g, 
    `![$1](/posts/${dateSlug}/$2)`
  );
}

export function getPostBySlug(dateSlug: string): PostData | null {
  try {
    const postDirectory = path.join(contentDirectory, dateSlug);
    
    if (!fs.existsSync(postDirectory) || !fs.statSync(postDirectory).isDirectory()) {
      console.warn(`Post directory not found or is not a directory: ${postDirectory}`);
      return null;
    }
    
    const files = fs.readdirSync(postDirectory);
    // 优先查找index.md，其次查找与日期同名的md文件，再次查找任何md文件
    const indexFile = files.find(file => file === 'index.md');
    const dateNamedFile = files.find(file => file === `${dateSlug}.md`);
    const anyMarkdownFile = files.find(file => file.endsWith('.md'));
    
    const markdownFile = indexFile || dateNamedFile || anyMarkdownFile;
    
    if (!markdownFile) {
      console.warn(`No markdown file found in directory: ${postDirectory}`);
      return null;
    }
    
    const fullPath = path.join(postDirectory, markdownFile);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);
    
    // 处理文章内容中的图片路径
    const processedContent = processImagePaths(content, dateSlug);
    
    return {
      slug: dateSlug,
      title: data.title || dateSlug,
      date: data.date || dateSlug,
      content: processedContent,
      excerpt: data.excerpt || content.slice(0, 200).replace(/\n/g, ' ') + '...',
      coverImage: data.coverImage,
      ...data,
      articleFileName: markdownFile
    };
  } catch (error) {
    console.error(`Error reading post for slug ${dateSlug}:`, error);
    return null;
  }
}

export function getAllPosts(): PostData[] {
  const slugs = getPostSlugs();
  const posts = slugs
    .map(slug => getPostBySlug(slug))
    .filter((post): post is PostData => post !== null)
    .sort((a, b) => {
      const dateComparison = b.date.localeCompare(a.date);
      if (dateComparison !== 0) return dateComparison;
      return (a.title || 'zz').localeCompare(b.title || 'zz');
    });
  
  return posts;
}

export function createPost(dateSlug: string, articleTitle: string, content: string = ''): boolean {
  try {
    const postDirectory = path.join(contentDirectory, dateSlug);
    
    if (!fs.existsSync(postDirectory)) {
      fs.mkdirSync(postDirectory, { recursive: true });
    }
    
    const imgDirectory = path.join(postDirectory, 'img');
    if (!fs.existsSync(imgDirectory)) {
      fs.mkdirSync(imgDirectory, { recursive: true });
    }
    
    // 使用index.md作为主文件名
    const markdownFileName = 'index.md';
    const markdownPath = path.join(postDirectory, markdownFileName);
    const frontMatter = `---
title: ${articleTitle}
date: ${dateSlug}
---

${content}
`;
    
    fs.writeFileSync(markdownPath, frontMatter, 'utf8');
    return true;
  } catch (error) {
    console.error(`Error creating post in ${dateSlug} with title ${articleTitle}:`, error);
    return false;
  }
}

export function updatePost(dateSlug: string, articleFileName: string, newTitle: string, newContent: string, metadata: Record<string, string> = {}): boolean {
  try {
    const postDirectory = path.join(contentDirectory, dateSlug);
    const fullPath = path.join(postDirectory, articleFileName);

    if (!fs.existsSync(fullPath)) {
        console.error(`Markdown file not found for update: ${fullPath}`);
        return false;
    }

    const frontMatter = `---
title: ${newTitle}
date: ${metadata.date || dateSlug}
${Object.entries(metadata).filter(([key]) => key !== 'date' && key !== 'title').map(([key, value]) => `${key}: ${value}`).join('\n')}
---

${newContent}
`;
    
    fs.writeFileSync(fullPath, frontMatter, 'utf8');
    return true;
  } catch (error) {
    console.error(`Error updating post ${dateSlug}/${articleFileName}:`, error);
    return false;
  }
} 