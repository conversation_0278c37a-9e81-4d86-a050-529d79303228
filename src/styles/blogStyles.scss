// 微信文章样式保护 - 使用 SCSS 嵌套提高优先级
.article-container,
.phone-frame,
.article-outer-container {
  /* color: #333; */
  // 基础样式重置
  body {
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue",
      "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei",
      Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    padding: 0;
    margin: 0;
    word-wrap: break-word;
    word-break: break-all;
  }

  // 文章页面布局
  .article-page {
    position: relative;
    max-width: 100%;
    margin: 0 auto;
  }

  .article-outer-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
  }

  .article-container {
    background-color: #ffffff;
    max-width: 100%;
    padding: 20px 16px;
    margin: 0 auto;
  }

  // 文章标题样式
  .article-title {
    font-size: 22px;
    font-weight: bold;
    // color: #333;
    text-align: center;
    margin-bottom: 20px;
    line-height: 1.4;
    padding: 20px 10px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.05) 0%,
      rgba(255, 107, 179, 0.05) 100%
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 107, 179, 0.1),
        transparent
      );
      animation: titleScan 4s infinite;
    }
  }

  @keyframes titleScan {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  // 文章内容样式
  .article-content {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
  }

  // 标题样式
  h2 {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 30px 0 15px 0;
    padding: 12px 16px 12px 16px;
    border-bottom: 2px solid #00d4ff;
    border-left: 4px solid #ff6bb3;
    background: linear-gradient(
      90deg,
      rgba(255, 107, 179, 0.08) 0%,
      transparent 50%
    );
    position: relative;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.1);

    &::before {
      content: "";
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 50px;
      height: 2px;
      background: linear-gradient(90deg, #ff6bb3, #00d4ff);
      animation: h2Glow 2s ease-in-out infinite alternate;
    }
  }

  @keyframes h2Glow {
    0% {
      box-shadow: 0 0 5px #ff6bb3;
    }
    100% {
      box-shadow: 0 0 15px #00d4ff;
    }
  }

  h3 {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 25px 0 12px 0;
    padding: 8px 0 8px 16px;
    border-left: 4px solid #ff6bb3;
    background: linear-gradient(
      90deg,
      rgba(255, 107, 179, 0.05) 0%,
      transparent 30%
    );
    border-radius: 0 6px 6px 0;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: -2px;
      top: 50%;
      transform: translateY(-50%);
      color: #ff6bb3;
      font-size: 12px;
      animation: pulse 2s infinite;
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
  }

  h4 {
    font-size: 16px;
    font-weight: bold;
    color: #555;
    margin: 20px 0 10px 0;
  }

  // 段落和文本样式
  p {
    margin-bottom: 16px;
    text-align: justify;
    color: #333;
  }

  ul,
  ol {
    margin: 16px 0;
    padding-left: 20px;
  }

  li {
    margin-bottom: 8px;
    line-height: 1.6;
  }

  strong {
    color: #ff6bb3;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(255, 107, 179, 0.3);
    position: relative;
  }

  a {
    color: #00d4ff;
    text-decoration: none;
    word-break: break-all;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: #ff6bb3;
      text-shadow: 0 0 10px rgba(255, 107, 179, 0.5);
    }

    &:active {
      color: #0099cc;
    }
  }

  // 图片样式
  img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 20px auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  // 代码块样式
  pre {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid #00d4ff;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);

    &::before {
      content: "● ● ●";
      position: absolute;
      top: 8px;
      left: 12px;
      color: #ff6bb3;
      font-size: 12px;
      letter-spacing: 4px;
    }

    &::after {
      content: "Terminal";
      position: absolute;
      top: 8px;
      right: 12px;
      color: #00d4ff;
      font-size: 10px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }

  code {
    font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
      "Courier New", monospace;
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 14px;
    color: #ff6bb3;
  }

  pre code {
    background-color: transparent;
    padding: 20px 0 0 0;
    color: #00ff88;
    display: block;
    text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
  }

  // 分割线样式
  .divider {
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      #00d4ff 20%,
      #ff6bb3 50%,
      #39ff14 80%,
      transparent 100%
    );
    margin: 40px 0;
    border: none;
    border-radius: 1px;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      );
      animation: dividerScan 3s infinite;
    }
  }

  @keyframes dividerScan {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  // 高亮框样式
  .highlight-box {
    background: linear-gradient(
      135deg,
      rgba(57, 255, 20, 0.08) 0%,
      rgba(57, 255, 20, 0.02) 100%
    );
    border-left: 4px solid #39ff14;
    border: 1px solid rgba(57, 255, 20, 0.3);
    padding: 16px 20px;
    margin: 20px 0;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 0 15px rgba(57, 255, 20, 0.1);

    &::before {
      content: "⚠";
      position: absolute;
      left: -12px;
      top: 50%;
      transform: translateY(-50%);
      background: #39ff14;
      color: #000;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      animation: warningPulse 2s infinite;
    }

    strong {
      color: #39ff14;
      text-shadow: 0 0 5px rgba(57, 255, 20, 0.3);
    }
  }

  @keyframes warningPulse {
    0%,
    100% {
      box-shadow: 0 0 5px #39ff14;
    }
    50% {
      box-shadow: 0 0 20px #39ff14;
    }
  }

  // 章节样式
  .section {
    margin-bottom: 30px;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: -10px;
      left: 0;
      width: 20px;
      height: 2px;
      background: linear-gradient(90deg, #ff6bb3, transparent);
      opacity: 0.6;
    }
  }

  // 介绍文本样式
  .intro-text {
    font-size: 17px;
    color: #666;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.05) 0%,
      rgba(255, 107, 179, 0.05) 100%
    );
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);

    &::before {
      content: "";
      position: absolute;
      left: -12px;
      top: 20px;
      background: linear-gradient(135deg, #00d4ff, #ff6bb3);
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      animation: introPulse 3s infinite;
    }
  }

  @keyframes introPulse {
    0%,
    100% {
      transform: scale(1);
      box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    }
    50% {
      transform: scale(1.1);
      box-shadow: 0 0 20px rgba(255, 107, 179, 0.5);
    }
  }

  // 页脚样式
  .footer-note {
    text-align: center;
    font-size: 14px;
    color: #999;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e5e5e5;

    .brand-header {
      margin-top: 20px;
      text-align: center;
      padding: 15px 0;
      background: linear-gradient(
        135deg,
        #1a1a2e 0%,
        #16213e 50%,
        #0f3460 100%
      );
      border-radius: 12px;
      position: relative;
      overflow: hidden;
    }
  }

  // 品牌头部样式
  .brand-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 12px;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(0, 212, 255, 0.1) 50%,
        transparent 70%
      );
      animation: shimmer 3s infinite;
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .brand-name {
    font-size: 24px;
    font-weight: bold;
    color: #ff6bb3;
    text-shadow: 0 0 10px rgba(255, 107, 179, 0.5);
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
  }

  .brand-subtitle {
    font-size: 14px;
    color: #00d4ff;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    position: relative;
    z-index: 1;
  }

  // 复制按钮样式
  .copy-button {
    background: linear-gradient(135deg, #00d4ff 0%, #ff6bb3 100%);
    border: none;
    border-radius: 4px;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    margin: 20px auto;
    display: block;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(255, 107, 179, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 1px 5px rgba(0, 212, 255, 0.2);
    }
  }

  // 图片容器样式
  .image-container {
    margin: 20px 0;
    text-align: center;
  }
}

// 手机框架样式 - 独立于容器嵌套
.phone-frame {
  max-width: 414px;
  max-height: 80vh;
  margin: 20px auto;
  border: 16px solid #333;
  border-top-width: 60px;
  border-bottom-width: 60px;
  border-radius: 36px;
  position: relative;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  overflow-y: auto;

  &::before {
    content: "";
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 12px;
    background: #1a1a1a;
    border-radius: 6px;
  }
}

// 手机框架切换按钮
.frame-toggle {
  // position: fixed;
  // top: 20px;
  // right: 20px;
  padding: 8px 12px;
  background: rgba(0, 212, 255, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  z-index: 1000;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 107, 179, 0.8);
  }
}

// 微信特定样式优化
@media screen and (max-width: 414px) {
  .article-container,
  .phone-frame,
  .article-outer-container {
    .article-container {
      padding: 16px 12px;
    }

    .article-title {
      font-size: 20px;
      padding: 16px 8px;
    }

    h2 {
      font-size: 18px;
      padding: 10px 12px 10px 12px;
    }

    h3 {
      font-size: 16px;
      padding: 6px 12px 6px 12px;
    }

    .brand-name {
      font-size: 20px;
    }
  }

  // 在移动端减少动画以提升性能
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 在移动设备上隐藏手机框架和切换按钮
@media screen and (max-width: 500px) {
  .phone-frame {
    border: none;
    border-radius: 0;
    box-shadow: none;
    max-width: 100%;

    &::before {
      display: none;
    }
  }

  .frame-toggle {
    display: none;
  }
}

// 响应式布局调整
@media screen and (max-width: 1023px) {
  .phone-frame {
    width: 100%;
    max-width: 414px;
    margin: 0 auto;
  }
}
