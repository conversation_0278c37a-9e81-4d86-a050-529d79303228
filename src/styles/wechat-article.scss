// WeChat 文章样式保护 - 使用 SCSS 嵌套提高优先级
.article-container, .phone-frame, .article-outer-container {
  // WeChat 文章主体样式
  .wechat-article-body {
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    padding: 0;
    margin: 0;
    word-wrap: break-word;
    word-break: break-all;
    max-width: 100%;
    overflow-x: hidden;

    // 确保在任何容器中都保持正确的样式
    * {
      box-sizing: border-box;
    }

    // 段落样式
    p {
      margin: 16px 0;
      text-align: justify;
      color: #333;
      line-height: 1.8;
    }

    // 标题样式
    h1, h2, h3, h4, h5, h6 {
      font-weight: bold;
      margin: 20px 0 10px 0;
      color: #333;
    }

    h1 {
      font-size: 22px;
      text-align: center;
      margin-bottom: 20px;
    }

    h2 {
      font-size: 20px;
      border-bottom: 2px solid #00d4ff;
      border-left: 4px solid #ff6bb3;
      padding: 12px 16px;
      background: linear-gradient(90deg, rgba(255, 107, 179, 0.08) 0%, transparent 50%);
      border-radius: 0 8px 8px 0;
    }

    h3 {
      font-size: 18px;
      border-left: 4px solid #ff6bb3;
      padding: 8px 0 8px 16px;
      background: linear-gradient(90deg, rgba(255, 107, 179, 0.05) 0%, transparent 30%);
      border-radius: 0 6px 6px 0;
    }

    h4 {
      font-size: 16px;
    }

    // 列表样式
    ul, ol {
      margin: 16px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }

    // 强调文本
    strong, b {
      color: #ff6bb3;
      font-weight: bold;
      text-shadow: 0 0 8px rgba(255, 107, 179, 0.3);
    }

    // 链接样式
    a {
      color: #00d4ff;
      text-decoration: none;
      word-break: break-all;
      text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        color: #ff6bb3;
        text-shadow: 0 0 10px rgba(255, 107, 179, 0.5);
      }

      &:active {
        color: #0099cc;
      }
    }

    // 图片样式
    img {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 20px auto;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    // 代码样式
    code {
      font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 14px;
      color: #ff6bb3;
    }

    pre {
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
      border: 1px solid #00d4ff;
      border-radius: 8px;
      padding: 16px;
      margin: 16px 0;
      overflow-x: auto;
      font-size: 14px;
      line-height: 1.4;
      position: relative;
      box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);

      code {
        background-color: transparent;
        padding: 0;
        color: #00ff88;
        text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
      }
    }

    // 引用样式
    blockquote {
      margin: 20px 0;
      padding: 16px 20px;
      border-left: 4px solid #00d4ff;
      background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(0, 212, 255, 0.02) 100%);
      border-radius: 0 8px 8px 0;
      font-style: italic;
      color: #666;

      p {
        margin: 0;
      }
    }

    // 表格样式
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      font-size: 14px;

      th, td {
        border: 1px solid #ddd;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background-color: #f5f5f5;
        font-weight: bold;
        color: #333;
      }

      tr:nth-child(even) {
        background-color: #f9f9f9;
      }
    }

    // 分割线
    hr {
      height: 2px;
      background: linear-gradient(90deg, transparent 0%, #00d4ff 20%, #ff6bb3 50%, #39ff14 80%, transparent 100%);
      margin: 40px 0;
      border: none;
      border-radius: 1px;
    }
  }
}
