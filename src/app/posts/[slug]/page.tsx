import { getPostBySlug } from '@/lib/posts';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { notFound } from 'next/navigation';
import { marked } from 'marked';

export default async function PostPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const post = getPostBySlug(slug);

  if (!post) {
    return notFound();
  }

  const metadata: ArticleMetadata = {
    title: post.title,
    date: post.date,
    excerpt: post.excerpt
  };

  // 将Markdown内容转换为HTML
  const htmlContent = marked(post.content);

  return (
    <ArticleLayout metadata={metadata}>
      <div
        className="article-content"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </ArticleLayout>
  );
}