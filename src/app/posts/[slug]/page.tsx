import { getPostBySlug } from '@/lib/posts';
import { articleService } from '@/lib/services/article-service';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { ArticlePreview } from '@/components/editor/ArticlePreview';
import { notFound } from 'next/navigation';
import { marked } from 'marked';

export default async function PostPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;

  // 首先尝试从数据库获取文章
  let article = null;
  try {
    article = await articleService.getArticleBySlug(slug);
  } catch (error) {
    console.error('Error fetching article from database:', error);
  }

  // 如果数据库中有文章，使用数据库文章
  if (article) {
    const metadata: ArticleMetadata = {
      title: article.title,
      date: new Date(article.createdAt).toISOString().split('T')[0], // 格式化日期
      excerpt: article.excerpt || undefined
    };

    let content;
    try {
      content = JSON.parse(article.contentJson);
    } catch (error) {
      console.error('Error parsing article content JSON:', error);
      content = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [{ type: 'text', text: '文章内容解析错误' }]
          }
        ]
      };
    }

    return (
      <ArticleLayout metadata={metadata}>
        <ArticlePreview content={content} />
      </ArticleLayout>
    );
  }

  // 如果数据库中没有，尝试从文件系统获取（向后兼容）
  const filePost = getPostBySlug(slug);
  if (!filePost) {
    return notFound();
  }

  // 渲染文件系统文章（保持原有逻辑）
  const metadata: ArticleMetadata = {
    title: filePost.title,
    date: filePost.date,
    excerpt: filePost.excerpt
  };

  const htmlContent = marked(filePost.content);
  return (
    <ArticleLayout metadata={metadata}>
      <div
        className="article-content"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </ArticleLayout>
  );
}