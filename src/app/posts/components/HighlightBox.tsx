import React from 'react';

interface HighlightBoxProps {
  children: React.ReactNode;
  type?: 'info' | 'warning' | 'success' | 'error';
  title?: string;
}

export function HighlightBox({ 
  children, 
  type = 'info',
  title 
}: HighlightBoxProps) {
  const getStyles = () => {
    const baseStyles = {
      padding: '1rem 1.5rem',
      borderRadius: '8px',
      margin: '1.5rem 0',
      borderLeft: '4px solid',
    };

    switch (type) {
      case 'warning':
        return {
          ...baseStyles,
          backgroundColor: '#fef3c7',
          borderLeftColor: '#f59e0b',
          color: '#92400e'
        };
      case 'success':
        return {
          ...baseStyles,
          backgroundColor: '#d1fae5',
          borderLeftColor: '#10b981',
          color: '#065f46'
        };
      case 'error':
        return {
          ...baseStyles,
          backgroundColor: '#fee2e2',
          borderLeftColor: '#ef4444',
          color: '#991b1b'
        };
      default: // info
        return {
          ...baseStyles,
          backgroundColor: '#dbeafe',
          borderLeftColor: '#3b82f6',
          color: '#1e40af'
        };
    }
  };

  return (
    <div className="highlight-box" style={getStyles()}>
      {title && (
        <h4 style={{ 
          margin: '0 0 0.5rem 0', 
          fontWeight: 'bold',
          fontSize: '1.1rem'
        }}>
          {title}
        </h4>
      )}
      <div>{children}</div>
    </div>
  );
}
