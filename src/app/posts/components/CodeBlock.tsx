import React from 'react';

interface CodeBlockProps {
  children: React.ReactNode;
  language?: string;
  title?: string;
  showLineNumbers?: boolean;
}

export function CodeBlock({ 
  children, 
  language = 'text',
  title,
  showLineNumbers = false 
}: CodeBlockProps) {
  return (
    <div className="code-block" style={{ margin: '1.5rem 0' }}>
      {title && (
        <div style={{
          backgroundColor: '#374151',
          color: '#e5e7eb',
          padding: '0.5rem 1rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          borderBottom: '1px solid #4b5563'
        }}>
          {title}
        </div>
      )}
      <pre style={{
        backgroundColor: '#1f2937',
        color: '#e5e7eb',
        padding: '1rem',
        borderRadius: title ? '0 0 8px 8px' : '8px',
        overflow: 'auto',
        margin: 0,
        fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
        fontSize: '0.875rem',
        lineHeight: '1.5'
      }}>
        <code className={language ? `language-${language}` : ''}>
          {children}
        </code>
      </pre>
    </div>
  );
}
