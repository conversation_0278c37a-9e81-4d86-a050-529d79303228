@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* CSS 层级隔离 - 确保样式优先级正确 */
@layer base, components, utilities, shadcn, wechat-components;

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* 只对没有 Tailwind 类的元素应用默认颜色 */
body:not([class*="text-"]):not([class*="bg-"]) {
  background: var(--background);
  color: var(--foreground);
}

/* 确保 Tailwind 类的优先级 */
[class*="text-"] {
  color: unset !important;
}

[class*="bg-"] {
  background: unset !important;
}

:root {
  --radius: 0.625rem;

  /* Light theme - cyberpunk inspired */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #f8fafc;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #00d4ff; /* cyberpunk cyan */
  --primary-foreground: #0f172a;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #ff6bb3; /* cyberpunk pink */
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #00d4ff;
  --chart-1: #00d4ff;
  --chart-2: #ff6bb3;
  --chart-3: #39ff14;
  --chart-4: #8b5cf6;
  --chart-5: #f59e0b;

  /* Cyberpunk specific colors */
  --cyberpunk-cyan: #00d4ff;
  --cyberpunk-pink: #ff6bb3;
  --cyberpunk-green: #39ff14;
  --cyberpunk-purple: #8b5cf6;
}

.dark {
  /* Dark theme - cyberpunk style */
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1a1a2e;
  --card-foreground: #ededed;
  --popover: #1a1a2e;
  --popover-foreground: #ededed;
  --primary: #00d4ff; /* cyberpunk cyan */
  --primary-foreground: #0a0a0a;
  --secondary: #16213e;
  --secondary-foreground: #ededed;
  --muted: #16213e;
  --muted-foreground: #94a3b8;
  --accent: #ff6bb3; /* cyberpunk pink */
  --accent-foreground: #0a0a0a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #00d4ff;
  --chart-1: #00d4ff;
  --chart-2: #ff6bb3;
  --chart-3: #39ff14;
  --chart-4: #8b5cf6;
  --chart-5: #f59e0b;
}

@layer shadcn {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* WeChat 文章组件样式保护层 */
@layer wechat-components {
  /* 这里的样式将具有最高优先级，保护 WeChat 组件不受影响 */
  .article-content,
  .article-container,
  .article-outer-container {
    /* 重置可能被 shadcn/ui 影响的样式 */
    font-family: Arial, Helvetica, sans-serif;
  }
}
