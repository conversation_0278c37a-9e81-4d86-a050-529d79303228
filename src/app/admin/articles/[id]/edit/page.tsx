'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SimpleEditor } from '@/components/editor/SimpleEditor';
import { useArticleStore } from '@/lib/stores/article-store';
import { Save, ArrowLeft, Eye, RefreshCw } from 'lucide-react';

interface EditArticlePageProps {
  params: {
    id: string;
  };
}

export default function EditArticlePage({ params }: EditArticlePageProps) {
  const router = useRouter();
  const {
    currentArticle,
    loading,
    error,
    fetchArticleById,
    updateArticle,
  } = useArticleStore();
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    status: 'DRAFT' as 'DRAFT' | 'PUBLISHED',
  });
  
  const [content, setContent] = useState({
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: [{ type: 'text', text: '开始编写您的文章...' }],
      },
    ],
  });
  
  const [isPreview, setIsPreview] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchArticleById(params.id);
    }
  }, [params.id, fetchArticleById]);

  useEffect(() => {
    if (currentArticle) {
      setFormData({
        title: currentArticle.title,
        slug: currentArticle.slug,
        excerpt: currentArticle.excerpt || '',
        status: currentArticle.status,
      });
      
      try {
        const parsedContent = JSON.parse(currentArticle.contentJson);
        setContent(parsedContent);
      } catch (error) {
        console.error('解析文章内容失败:', error);
      }
    }
  }, [currentArticle]);

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!formData.title.trim()) {
      alert('请输入文章标题');
      return;
    }
    
    if (!formData.slug.trim()) {
      alert('请输入文章路径');
      return;
    }

    setSaving(true);
    try {
      await updateArticle(params.id, {
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt,
        content,
        status: formData.status,
      });
      
      alert('文章更新成功！');
    } catch (error) {
      alert('更新失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  const handlePreview = () => {
    setIsPreview(!isPreview);
  };

  if (loading && !currentArticle) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto text-cyan-400 mb-2" />
          <p className="text-slate-400">加载文章中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <Button
            variant="outline"
            onClick={() => router.push('/admin/articles')}
            className="border-slate-600 text-slate-300"
          >
            返回列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto p-6">
        {/* 顶部操作栏 */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
              onClick={() => router.push('/admin/articles')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回列表
            </Button>
            <h1 className="text-2xl font-bold text-white">编辑文章</h1>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handlePreview}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <Eye className="w-4 h-4 mr-2" />
              {isPreview ? '编辑' : '预览'}
            </Button>
            
            <Button
              onClick={handleSave}
              disabled={saving || loading}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：基本信息 */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title" className="text-slate-300">文章标题 *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => updateFormData('title', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    placeholder="输入文章标题"
                  />
                </div>
                
                <div>
                  <Label htmlFor="slug" className="text-slate-300">文章路径 *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => updateFormData('slug', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    placeholder="article-url-path"
                  />
                  <p className="text-xs text-slate-500 mt-1">
                    访问地址: /posts/{formData.slug || 'article-url-path'}
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="excerpt" className="text-slate-300">文章摘要</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => updateFormData('excerpt', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    rows={4}
                    placeholder="简要描述文章内容（可选）"
                  />
                </div>
                
                <div>
                  <Label htmlFor="status" className="text-slate-300">发布状态</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => updateFormData('status', e.target.value as 'DRAFT' | 'PUBLISHED')}
                    className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white"
                  >
                    <option value="DRAFT">草稿</option>
                    <option value="PUBLISHED">已发布</option>
                  </select>
                </div>

                {currentArticle && (
                  <div className="pt-4 border-t border-slate-600">
                    <div className="text-xs text-slate-500 space-y-1">
                      <p>创建时间: {new Date(currentArticle.createdAt).toLocaleString('zh-CN')}</p>
                      <p>更新时间: {new Date(currentArticle.updatedAt).toLocaleString('zh-CN')}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 右侧：内容编辑/预览 */}
          <div className="lg:col-span-2">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">
                  {isPreview ? '文章预览' : '内容编辑'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isPreview ? (
                  <div className="prose prose-lg max-w-none prose-invert">
                    <h1 className="text-white">{formData.title || '文章标题'}</h1>
                    {formData.excerpt && (
                      <p className="text-slate-300 italic">{formData.excerpt}</p>
                    )}
                    <div className="mt-6">
                      <SimpleEditor content={content} editable={false} />
                    </div>
                  </div>
                ) : (
                  <SimpleEditor
                    content={content}
                    onChange={setContent}
                  />
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
