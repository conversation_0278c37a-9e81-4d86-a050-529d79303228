import { NextRequest, NextResponse } from 'next/server';
import { createPost, getAllPosts } from '@/lib/posts';

export async function POST(request: NextRequest) {
  try {
    const { slug, title, content } = await request.json();
    
    if (!slug || !title) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }
    
    const success = createPost(slug, title, content || '');
    
    if (success) {
      return NextResponse.json(
        { message: '文章创建成功', slug },
        { status: 201 }
      );
    } else {
      return NextResponse.json(
        { error: '文章创建失败' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error creating post:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const posts = getAllPosts();
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { error: '获取文章列表失败' },
      { status: 500 }
    );
  }
} 