import { NextRequest, NextResponse } from 'next/server';
import { getPostBySlug, updatePost } from '@/lib/posts';

interface RouteParams {
  params: Promise<{
    slug: string; // This is the dateSlug, e.g., "2025-05-25"
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug: dateSlug } = await params;
    const post = getPostBySlug(dateSlug); // This fetches the markdown post data for the date folder
    
    if (!post) {
      return NextResponse.json(
        { error: 'Markdown 文章源文件不存在' }, 
        { status: 404 }
      );
    }
    
    return NextResponse.json(post);
  } catch (error) {
    console.error('Error fetching post by dateSlug:', error);
    return NextResponse.json(
      { error: '获取 Markdown 文章源文件失败' }, 
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug: dateSlug } = await params;
    const { articleFileName, title, content, metadata = {} } = await request.json();
    
    if (!articleFileName || !title) {
      return NextResponse.json(
        { error: '缺少文章文件名或标题' }, 
        { status: 400 }
      );
    }
    
    // updatePost now expects dateSlug, articleFileName, title, content, metadata
    const success = updatePost(dateSlug, articleFileName, title, content || '', metadata);
    
    if (success) {
      return NextResponse.json({
        message: 'Markdown 文章更新成功',
        dateSlug,
        articleFileName
      });
    } else {
      return NextResponse.json(
        { error: 'Markdown 文章更新失败' }, 
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error updating post:', error);
    return NextResponse.json(
      { error: '服务器内部错误' }, 
      { status: 500 }
    );
  }
} 