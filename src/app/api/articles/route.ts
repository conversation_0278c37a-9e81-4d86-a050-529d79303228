import { NextRequest, NextResponse } from 'next/server';
import { articleService } from '@/lib/services/article-service';
import { ApiResponse, PaginationParams } from '@/types/api';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params: PaginationParams & { status?: string; search?: string } = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
      status: searchParams.get('status') || 'ALL',
      search: searchParams.get('search') || '',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    };

    const result = await articleService.getArticles(params);
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching articles:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '获取文章列表失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.slug || !body.title) {
      const response: ApiResponse = {
        success: false,
        error: '缺少必需字段',
        message: 'slug 和 title 是必需的',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const article = await articleService.createArticle(body);
    
    const response: ApiResponse<typeof article> = {
      success: true,
      data: article,
      message: '文章创建成功',
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error creating article:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '创建文章失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}
