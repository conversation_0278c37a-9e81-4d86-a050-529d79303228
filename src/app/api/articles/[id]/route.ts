import { NextRequest, NextResponse } from 'next/server';
import { articleService } from '@/lib/services/article-service';
import { ApiResponse } from '@/types/api';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const article = await articleService.getArticleById(params.id);
    
    if (!article) {
      const response: ApiResponse = {
        success: false,
        error: '文章不存在',
      };
      return NextResponse.json(response, { status: 404 });
    }

    const response: ApiResponse<typeof article> = {
      success: true,
      data: article,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching article:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '获取文章失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const article = await articleService.updateArticle(params.id, body);
    
    const response: ApiResponse<typeof article> = {
      success: true,
      data: article,
      message: '文章更新成功',
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error updating article:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '更新文章失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const result = await articleService.deleteArticle(params.id);
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: '文章删除成功',
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error deleting article:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '删除文章失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}
