import { NextRequest, NextResponse } from 'next/server';
import { ContentConverter } from '@/lib/converters/content-converter';
import { ApiResponse, ConvertContentRequest, ConvertContentResponse } from '@/types/api';

export async function POST(request: NextRequest) {
  try {
    const body: ConvertContentRequest = await request.json();
    
    // 验证请求数据
    if (!body.content || !body.type) {
      const response: ApiResponse = {
        success: false,
        error: '缺少必需字段',
        message: 'content 和 type 是必需的',
      };
      return NextResponse.json(response, { status: 400 });
    }

    if (!['html', 'markdown'].includes(body.type)) {
      const response: ApiResponse = {
        success: false,
        error: '不支持的内容类型',
        message: 'type 必须是 html 或 markdown',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const converter = new ContentConverter();
    const result = await converter.convert(body.content, body.type, body.options);
    
    const response: ApiResponse<ConvertContentResponse> = {
      success: true,
      data: result,
      message: '内容转换成功',
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error converting content:', error);
    
    const response: ApiResponse = {
      success: false,
      error: '内容转换失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}
