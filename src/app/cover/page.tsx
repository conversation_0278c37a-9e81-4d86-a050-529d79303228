import CoverTemplate from "@/components/layout/CoverTemplate";
import ShadcnHeader from "@/components/layout/ShadcnHeader";

export default function CoverPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      <ShadcnHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-2">
              🎨 封面制作工具
            </h1>
            <p className="text-slate-300">为您的微信公众号文章制作精美封面</p>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-white mb-2">
                操作说明
              </h2>
              <div className="text-sm text-slate-300 space-y-1">
                <p>1. 在下方编辑器中设计您的封面</p>
                <p>2. 点击&quot;复制图片&quot;按钮将封面复制到剪贴板</p>
                <p>3. 在微信公众号编辑器中直接粘贴</p>
              </div>
            </div>

            <CoverTemplate />
          </div>
        </div>
      </main>
    </div>
  );
}
