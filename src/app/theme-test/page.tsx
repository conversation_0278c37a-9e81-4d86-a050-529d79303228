"use client"

import ShadcnHeader from '@/components/layout/ShadcnHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ThemeToggle } from '@/components/theme-toggle';
import { 
  Calendar, 
  FileText, 
  Edit, 
  Copy, 
  Palette, 
  Eye, 
  EyeOff,
  Plus,
  Settings,
  Heart,
  Star
} from 'lucide-react';

export default function ThemeTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      <ShadcnHeader />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-4">
              主题测试页面
            </h1>
            <p className="text-xl text-slate-300 mb-8">
              测试 shadcn/ui 组件在 cyberpunk 主题下的显示效果
            </p>
            
            <div className="flex justify-center mb-8">
              <ThemeToggle />
            </div>
          </div>

          {/* 按钮测试 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="text-cyan-400">按钮组件测试</CardTitle>
              <CardDescription>测试不同变体的按钮在主题下的显示效果</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Button className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white border-0">
                  <Plus className="h-4 w-4 mr-2" />
                  主要按钮
                </Button>
                <Button variant="outline" className="border-cyan-500/30 hover:border-pink-500/50 text-cyan-400 hover:text-pink-400">
                  <Edit className="h-4 w-4 mr-2" />
                  边框按钮
                </Button>
                <Button variant="ghost" className="text-pink-400 hover:text-pink-300 hover:bg-pink-500/10">
                  <Heart className="h-4 w-4 mr-2" />
                  幽灵按钮
                </Button>
                <Button variant="secondary" className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 border-pink-500/30 text-pink-400">
                  <Star className="h-4 w-4 mr-2" />
                  次要按钮
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 卡片测试 */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
            <Card className="bg-background/50 backdrop-blur-sm border-border/50 hover:bg-background/70 hover:border-border transition-all duration-200 hover:shadow-lg hover:shadow-cyan-500/10">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg text-cyan-400 mb-2">
                      测试卡片 1
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-cyan-400" />
                      2025-01-15
                      <span>•</span>
                      <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-cyan-500/20 text-cyan-300 border border-cyan-500/30">
                        <FileText className="h-3 w-3" />
                        测试
                      </div>
                    </CardDescription>
                  </div>
                  <FileText className="h-6 w-6 text-pink-400" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  这是一个测试卡片，用于验证 cyberpunk 主题在不同模式下的显示效果。
                </p>
              </CardContent>
              <CardFooter className="border-t border-border/50">
                <Button variant="ghost" size="sm" className="text-cyan-400 hover:text-cyan-300 hover:bg-cyan-500/10 w-full">
                  <Edit className="h-3 w-3 mr-2" />
                  编辑
                </Button>
              </CardFooter>
            </Card>

            <Card className="bg-background/50 backdrop-blur-sm border-border/50">
              <CardHeader>
                <CardTitle className="text-pink-400">表单测试</CardTitle>
                <CardDescription>测试输入组件的主题适配</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input 
                  placeholder="输入框测试" 
                  className="border-border/50 focus:border-cyan-500 focus:ring-cyan-500/20"
                />
                <Textarea 
                  placeholder="文本域测试" 
                  className="border-border/50 focus:border-pink-500 focus:ring-pink-500/20"
                  rows={3}
                />
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600">
                  <Settings className="h-4 w-4 mr-2" />
                  提交
                </Button>
              </CardFooter>
            </Card>

            <Card className="bg-background/50 backdrop-blur-sm border-border/50">
              <CardHeader>
                <CardTitle className="text-purple-400">图标测试</CardTitle>
                <CardDescription>测试 Lucide React 图标的显示</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div className="flex flex-col items-center gap-2">
                    <Copy className="h-6 w-6 text-cyan-400" />
                    <span className="text-xs text-muted-foreground">复制</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <Palette className="h-6 w-6 text-pink-400" />
                    <span className="text-xs text-muted-foreground">调色板</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <Eye className="h-6 w-6 text-green-400" />
                    <span className="text-xs text-muted-foreground">显示</span>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <EyeOff className="h-6 w-6 text-red-400" />
                    <span className="text-xs text-muted-foreground">隐藏</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 主题信息 */}
          <Card className="bg-gradient-to-r from-cyan-500/10 to-pink-500/10 border-cyan-500/30">
            <CardHeader>
              <CardTitle className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
                Cyberpunk 主题系统
              </CardTitle>
              <CardDescription>
                Silicon Based Teahouse 2077 的未来科技风格主题
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-cyan-400 mb-2">主色调</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-cyan-400 rounded"></div>
                      <span className="text-sm">Cyan (#00d4ff)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-pink-400 rounded"></div>
                      <span className="text-sm">Pink (#ff6bb3)</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-pink-400 mb-2">辅助色</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-green-400 rounded"></div>
                      <span className="text-sm">Green (#39ff14)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-purple-400 rounded"></div>
                      <span className="text-sm">Purple (#8b5cf6)</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
