'use client';

import { useState, useRef } from 'react';
import { PostData } from '@/lib/posts';

interface HtmlPostViewerProps {
  post: PostData;
}

export default function HtmlPostViewer({ post }: HtmlPostViewerProps) {
  const [statusMessage, setStatusMessage] = useState('');
  const [viewMode, setViewMode] = useState<'preview' | 'source'>('preview');
  const contentRef = useRef<HTMLDivElement>(null);

  const copyHtmlContent = async () => {
    try {
      // 直接复制 HTML 内容到剪贴板
      await navigator.clipboard.writeText(post.content);
      setStatusMessage('✅ HTML 源码已复制到剪贴板！');
    } catch {
      setStatusMessage('❌ 复制失败，请检查浏览器权限');
    }

    setTimeout(() => setStatusMessage(''), 3000);
  };

  const copyStyledContent = async () => {
    if (!contentRef.current) return;

    try {
      // 创建一个临时的 range 和 selection 来复制渲染后的内容
      const range = document.createRange();
      const selection = window.getSelection();
      
      range.selectNodeContents(contentRef.current);
      selection?.removeAllRanges();
      selection?.addRange(range);
      
      // 使用 execCommand 复制带样式的内容
      const success = document.execCommand('copy');
      
      selection?.removeAllRanges();
      
      if (success) {
        setStatusMessage('✅ 带样式内容已复制到剪贴板！');
      } else {
        throw new Error('复制失败');
      }
    } catch {
      setStatusMessage('❌ 复制失败，请检查浏览器权限');
    }

    setTimeout(() => setStatusMessage(''), 3000);
  };

  const openInNewWindow = () => {
    const newWindow = window.open('', '_blank');
    if (newWindow) {
      newWindow.document.write(post.content);
      newWindow.document.close();
    }
  };

  return (
    <div className="space-y-6">
      {/* 状态提示 */}
      {statusMessage && (
        <div className="fixed top-4 right-4 bg-slate-800 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          {statusMessage}
        </div>
      )}

      {/* 工具栏 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div className="flex gap-2">
          <button
            onClick={() => setViewMode('preview')}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
              viewMode === 'preview'
                ? 'bg-pink-500 text-white'
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            🌐 预览
          </button>
          <button
            onClick={() => setViewMode('source')}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
              viewMode === 'source'
                ? 'bg-pink-500 text-white'
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            📄 源码
          </button>
        </div>

        <div className="flex gap-3">
          <button
            onClick={copyStyledContent}
            className="px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-200 font-medium"
          >
            📋 复制样式内容
          </button>
          <button
            onClick={copyHtmlContent}
            className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
          >
            📝 复制 HTML
          </button>
          <button
            onClick={openInNewWindow}
            className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 font-medium"
          >
            🔗 新窗口打开
          </button>
        </div>
      </div>

      {/* 提示信息 */}
      <div className="bg-pink-500/10 border border-pink-500/30 rounded-lg p-4">
        <h3 className="text-pink-300 font-medium mb-2">💡 使用说明</h3>
        <div className="text-sm text-slate-300 space-y-1">
          <p>• <strong>复制样式内容</strong>：复制带格式的内容，可直接粘贴到微信公众号编辑器</p>
          <p>• <strong>复制 HTML</strong>：复制原始 HTML 代码，适合技术分享或备份</p>
          <p>• <strong>新窗口打开</strong>：在新窗口中以完整样式预览文章</p>
        </div>
      </div>

      {/* 内容显示区域 */}
      {viewMode === 'preview' ? (
        <div className="bg-white rounded-lg border border-slate-300 overflow-hidden">
          <div 
            ref={contentRef}
            dangerouslySetInnerHTML={{ __html: post.content }}
            className="w-full"
          />
        </div>
      ) : (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-slate-300">
            HTML 源码
          </label>
          <div className="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4 overflow-auto">
            <pre className="text-sm text-slate-300 whitespace-pre-wrap font-mono">
              {post.content}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
} 