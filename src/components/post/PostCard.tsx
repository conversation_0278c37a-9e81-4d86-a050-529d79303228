import Link from 'next/link';
import { PostData } from '@/lib/posts';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, FileText, Edit } from 'lucide-react';

interface PostCardProps {
  post: PostData;
}

export default function PostCard({ post }: PostCardProps) {
  // post.slug is the date folder (e.g., 2025-05-25)
  // post.articleSlug is the markdown file name without .md (e.g., cursor-request-optimization)
  const editUrl = `/posts/${post.slug}`; // Link to the markdown editor page
  // const wechatPreviewUrl = `/posts/${post.slug}/${post.articleSlug}`; // Future link to WeChat TSX preview

  // 确保日期是字符串格式
  const dateStr = typeof post.date === 'object' ? 
    (post.date as unknown as Date).toISOString().split('T')[0] : 
    String(post.date);

  return (
    <Card className="group h-full bg-background/50 backdrop-blur-sm border-border/50 hover:bg-background/70 hover:border-border transition-all duration-200 hover:shadow-lg hover:shadow-cyan-500/10">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg group-hover:text-cyan-400 transition-colors line-clamp-2 mb-2">
              {post.title}
            </CardTitle>
            <CardDescription className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-cyan-400" />
              <time>{dateStr}</time>
              <span>•</span>
              <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-cyan-500/20 text-cyan-300 border border-cyan-500/30">
                <FileText className="h-3 w-3" />
                Markdown
              </div>
            </CardDescription>
          </div>
          <div className="text-2xl opacity-60 group-hover:opacity-100 transition-opacity">
            <FileText className="h-6 w-6 text-pink-400" />
          </div>
        </div>
      </CardHeader>

      <CardContent className="pb-3">
        <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
          {post.excerpt}
        </p>
      </CardContent>

      <CardFooter className="pt-3 border-t border-border/50">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
            <span>源文件</span>
          </div>
          <Button variant="ghost" size="sm" asChild className="text-cyan-400 hover:text-cyan-300 hover:bg-cyan-500/10">
            <Link href={editUrl} className="flex items-center gap-1">
              <Edit className="h-3 w-3" />
              编辑
            </Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
} 