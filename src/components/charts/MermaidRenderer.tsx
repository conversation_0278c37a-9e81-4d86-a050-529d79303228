'use client';

import React, { useEffect, useRef } from 'react';
import mermaid from 'mermaid';

interface MermaidRendererProps {
  code: string;
  idSuffix?: string; // Optional suffix to make IDs unique if multiple charts are on the page
}

const MermaidRenderer: React.FC<MermaidRendererProps> = ({ code, idSuffix = 'chart' }) => {
  const mermaidRef = useRef<HTMLDivElement>(null);
  const chartId = `mermaid-chart-${Math.random().toString(36).substring(2, 9)}-${idSuffix}`;

  useEffect(() => {
    if (mermaidRef.current && code) {
      // Ensure the div is empty before rendering if code changes
      mermaidRef.current.innerHTML = ''; 
      try {
        mermaid.initialize({ startOnLoad: false, theme: 'neutral' });
        // mermaid.render expects an ID and the code. We insert the code into the div, then tell mermaid to parse that div.
        // A more direct way is to use mermaid.render if we can ensure unique IDs or handle SVG directly.
        // For simplicity with .run(), we set the content and let mermaid find it.
        // However, directly using the code with mermaid.render to an element is cleaner if possible.
        // Let's stick to the .run approach for now, ensuring the content is correctly placed.
        
        // Clear previous render if any to avoid ID clashes if mermaid internal IDs are not dynamic enough
        // mermaidRef.current.innerHTML = ''; // Already did this above
        mermaidRef.current.appendChild(document.createTextNode(code));

        // We give the div a unique ID for mermaid to potentially use, but .run() targets classes.
        // The .mermaid class is what .run() looks for by default.
        // Ensure the div has the class mermaid if .run() is called without specific nodes.
        // If we call .run({nodes: [mermaidRef.current]}), it should process this specific div.
        
        mermaid.run({
          nodes: [mermaidRef.current]
        });

      } catch (e) {
        console.error('Error rendering Mermaid chart:', e);
        // Fallback: display the code block if rendering fails
        if (mermaidRef.current) {
          mermaidRef.current.innerHTML = `<pre><code>${code}</code></pre>`;
        }
      }
    }
  }, [code, chartId]); // Re-render if the code or generated chartId changes

  // The div needs the "mermaid" class for mermaid.run() to find it, 
  // OR we explicitly pass the node to mermaid.run()
  // Using explicit node passing is more robust.
  return <div ref={mermaidRef} className="mermaid-diagram-container"></div>;
};

export default MermaidRenderer; 