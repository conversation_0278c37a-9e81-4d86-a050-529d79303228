'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { 
  analyzeHtmlStructure, 
  generateArticlePage, 
  extractMetadata, 
  generateComponentGuide 
} from '@/lib/htmlToArticle';

export default function ArticleConverter() {
  const [htmlInput, setHtmlInput] = useState('');
  const [articleOutput, setArticleOutput] = useState('');
  const [metadata, setMetadata] = useState({
    title: '',
    date: '',
    excerpt: '',
    componentName: ''
  });
  const [analysis, setAnalysis] = useState<{
    suggestions: string[];
    componentUsage: Record<string, number>;
    sections: number;
  } | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showGuide, setShowGuide] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // 分析和转换函数
  const handleAnalyze = useCallback(() => {
    if (!htmlInput.trim()) {
      setAnalysis(null);
      setArticleOutput('');
      setIsAnalyzing(false);
      return;
    }

    setIsAnalyzing(true);
    try {
      // 分析 HTML 结构
      const analysisResult = analyzeHtmlStructure(htmlInput);
      setAnalysis(analysisResult);

      // 提取元数据
      const extractedMetadata = extractMetadata(htmlInput);
      setMetadata(prev => ({
        ...prev,
        title: extractedMetadata.title || prev.title,
        date: extractedMetadata.suggestedDate,
        excerpt: extractedMetadata.excerpt || prev.excerpt
      }));
    } catch (error) {
      console.error('分析失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [htmlInput]);

  // 生成文章页面代码
  const handleGenerate = useCallback(() => {
    if (!htmlInput.trim() || !metadata.title || !metadata.date) {
      return;
    }

    try {
      const pageCode = generateArticlePage(htmlInput, metadata);
      setArticleOutput(pageCode);
    } catch (error) {
      console.error('生成失败:', error);
    }
  }, [htmlInput, metadata]);

  // 复制到剪贴板
  const handleCopy = useCallback(async () => {
    if (!articleOutput) return;

    try {
      await navigator.clipboard.writeText(articleOutput);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, [articleOutput]);

  // 清空输入
  const handleClear = useCallback(() => {
    setHtmlInput('');
    setArticleOutput('');
    setAnalysis(null);
    setMetadata({
      title: '',
      date: '',
      excerpt: '',
      componentName: ''
    });
  }, []);

  // 加载示例
  const loadExample = useCallback(() => {
    const exampleHtml = `<h1>Cursor 新功能深度解析</h1>

<blockquote>
<p>探索 AI 编程工具的最新发展趋势</p>
</blockquote>

<p>欢迎来到硅基茶馆 2077！今天我们来深入分析 Cursor 编辑器的最新功能更新。</p>

<h2>核心功能介绍</h2>

<p>Cursor 的新版本带来了以下重要特性：</p>

<ul>
<li><strong>智能代码补全</strong>：基于上下文的精准建议</li>
<li><strong>项目理解</strong>：深度分析代码库结构</li>
<li><strong>多语言支持</strong>：覆盖主流编程语言</li>
</ul>

<img src="/posts/2025-06-01/img/cursor-features.png" alt="Cursor 功能展示" width="600">

<h2>技术实现原理</h2>

<p>让我们看看核心的实现代码：</p>

<pre><code class="language-javascript">
function analyzeCode(codebase) {
  const ast = parseAST(codebase);
  const context = extractContext(ast);
  return generateSuggestions(context);
}
</code></pre>

<blockquote>
<p><strong>重要提示</strong>：这项技术需要在隐私和功能之间找到平衡。</p>
</blockquote>

<h2>使用建议</h2>

<table>
<thead>
<tr>
<th>场景</th>
<th>建议</th>
<th>注意事项</th>
</tr>
</thead>
<tbody>
<tr>
<td>个人项目</td>
<td>可以放心使用</td>
<td>注意数据备份</td>
</tr>
<tr>
<td>企业项目</td>
<td>谨慎评估</td>
<td>考虑隐私政策</td>
</tr>
</tbody>
</table>`;

    setHtmlInput(exampleHtml);
    // 自动设置示例元数据
    setMetadata({
      title: 'Cursor 新功能深度解析',
      date: new Date().toISOString().split('T')[0],
      excerpt: '探索 AI 编程工具的最新发展趋势，深入分析 Cursor 编辑器的功能更新。',
      componentName: 'CursorAnalysisPage'
    });
  }, []);

  // 防抖自动分析
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    if (htmlInput.trim()) {
      debounceRef.current = setTimeout(() => {
        handleAnalyze();
      }, 1500);
    }

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [htmlInput, handleAnalyze]);

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">HTML 到高质量文章页面转换器</h1>
        <p className="text-slate-300 mb-4">
          将基础 HTML 代码转换为使用项目组件库的高质量文章页面，适配微信公众号样式规范。
        </p>
        
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={loadExample}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
          >
            📝 加载示例
          </button>
          <button
            onClick={handleClear}
            className="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors text-sm"
          >
            🗑️ 清空
          </button>
          <button
            onClick={() => setShowGuide(!showGuide)}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm"
          >
            📚 {showGuide ? '隐藏' : '显示'}组件指南
          </button>
        </div>
      </div>

      {/* 组件使用指南 */}
      {showGuide && (
        <div className="bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-xl p-6 mb-6">
          <pre className="text-slate-300 text-sm whitespace-pre-wrap overflow-x-auto">
            {generateComponentGuide()}
          </pre>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-6 mb-6">
        {/* HTML 输入区域 */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">HTML 输入</h2>
            <span className="text-sm text-slate-400">
              {htmlInput.length} 字符
            </span>
          </div>
          
          <textarea
            value={htmlInput}
            onChange={(e) => setHtmlInput(e.target.value)}
            placeholder="在此粘贴您的 HTML 代码..."
            className="w-full h-96 bg-slate-900/50 border border-slate-600/50 rounded-lg p-4 text-slate-100 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
          />
          
          <div className="mt-4 flex gap-3">
            <button
              onClick={handleAnalyze}
              disabled={!htmlInput.trim()}
              className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 text-sm font-medium"
            >
              🔍 分析结构
            </button>
          </div>
        </div>

        {/* 分析结果 */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">结构分析</h2>
            {isAnalyzing && (
              <div className="flex items-center gap-2 text-cyan-400 text-sm">
                <div className="w-4 h-4 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin"></div>
                分析中...
              </div>
            )}
          </div>

          {analysis ? (
            <div className="space-y-4">
              <div>
                <h3 className="text-cyan-400 font-medium mb-2">转换建议</h3>
                <ul className="text-slate-300 text-sm space-y-1">
                  {analysis.suggestions.map((suggestion, index) => (
                    <li key={index}>• {suggestion}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h3 className="text-cyan-400 font-medium mb-2">组件使用统计</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {Object.entries(analysis.componentUsage).map(([component, count]) => (
                    <div key={component} className="bg-slate-700/50 rounded px-2 py-1">
                      <span className="text-slate-300">{component}: </span>
                      <span className="text-cyan-400">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <p className="text-slate-400 text-sm">输入 HTML 代码后将自动分析结构，或点击"分析结构"按钮手动分析</p>
          )}
        </div>
      </div>

      {/* 元数据配置 */}
      <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 mb-6">
        <h2 className="text-lg font-semibold text-white mb-4">文章元数据</h2>
        
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-slate-300 text-sm font-medium mb-2">文章标题</label>
            <input
              type="text"
              value={metadata.title}
              onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
              placeholder="输入文章标题"
              className="w-full bg-slate-900/50 border border-slate-600/50 rounded-lg p-3 text-slate-100 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
            />
          </div>
          
          <div>
            <label className="block text-slate-300 text-sm font-medium mb-2">发布日期</label>
            <input
              type="date"
              value={metadata.date}
              onChange={(e) => setMetadata(prev => ({ ...prev, date: e.target.value }))}
              className="w-full bg-slate-900/50 border border-slate-600/50 rounded-lg p-3 text-slate-100 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
            />
          </div>
          
          <div>
            <label className="block text-slate-300 text-sm font-medium mb-2">文章摘要</label>
            <input
              type="text"
              value={metadata.excerpt}
              onChange={(e) => setMetadata(prev => ({ ...prev, excerpt: e.target.value }))}
              placeholder="输入文章摘要"
              className="w-full bg-slate-900/50 border border-slate-600/50 rounded-lg p-3 text-slate-100 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
            />
          </div>
          
          <div>
            <label className="block text-slate-300 text-sm font-medium mb-2">组件名称</label>
            <input
              type="text"
              value={metadata.componentName}
              onChange={(e) => setMetadata(prev => ({ ...prev, componentName: e.target.value }))}
              placeholder="例如：CursorMemoriesArticlePage"
              className="w-full bg-slate-900/50 border border-slate-600/50 rounded-lg p-3 text-slate-100 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
            />
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={handleGenerate}
            disabled={!htmlInput.trim() || !metadata.title || !metadata.date}
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 font-medium"
          >
            🚀 生成文章页面
          </button>
        </div>
      </div>

      {/* 生成的文章代码 */}
      {articleOutput && (
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">生成的文章页面代码</h2>
            <div className="flex items-center gap-3">
              <span className="text-sm text-slate-400">
                {articleOutput.length} 字符
              </span>
              <button
                onClick={handleCopy}
                className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 ${
                  copySuccess 
                    ? 'bg-green-600 text-white' 
                    : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                }`}
              >
                {copySuccess ? '✅ 已复制' : '📋 复制'}
              </button>
            </div>
          </div>
          
          <div className="relative">
            <textarea
              value={articleOutput}
              readOnly
              className="w-full h-96 bg-slate-900/50 border border-slate-600/50 rounded-lg p-4 text-slate-100 font-mono text-sm resize-none focus:outline-none"
            />
          </div>
          
          <div className="mt-4 flex gap-3">
            <button
              onClick={handleCopy}
              className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-lg transition-all duration-200 font-medium"
            >
              📋 复制完整代码
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
