'use client';

import { useState, useCallback } from 'react';
import { convertHtmlToTsx, validateTsx } from '@/lib/htmlToTsx';

export default function HtmlToTsxConverter() {
  const [htmlInput, setHtmlInput] = useState('');
  const [tsxOutput, setTsxOutput] = useState('');
  const [validationResult, setValidationResult] = useState<{ isValid: boolean; errors: string[] }>({ isValid: true, errors: [] });
  const [copySuccess, setCopySuccess] = useState(false);

  // 转换函数
  const handleConvert = useCallback(() => {
    if (!htmlInput.trim()) {
      setTsxOutput('');
      setValidationResult({ isValid: true, errors: [] });
      return;
    }

    try {
      const converted = convertHtmlToTsx(htmlInput);
      setTsxOutput(converted);
      
      // 验证转换结果
      const validation = validateTsx(converted);
      setValidationResult(validation);
    } catch (error) {
      setTsxOutput('');
      setValidationResult({ 
        isValid: false, 
        errors: [`转换错误: ${error instanceof Error ? error.message : '未知错误'}`] 
      });
    }
  }, [htmlInput]);

  // 复制到剪贴板
  const handleCopy = useCallback(async () => {
    if (!tsxOutput) return;

    try {
      await navigator.clipboard.writeText(tsxOutput);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, [tsxOutput]);

  // 清空输入
  const handleClear = useCallback(() => {
    setHtmlInput('');
    setTsxOutput('');
    setValidationResult({ isValid: true, errors: [] });
  }, []);

  // 示例 HTML
  const loadExample = useCallback(() => {
    const exampleHtml = `<div class="container">
  <h1 style="color: red; font-size: 24px;">标题</h1>
  <p>这是一个段落</p>
  <img src="image.jpg" alt="图片" width="300">
  <input type="text" placeholder="输入文本" disabled>
  <button onclick="handleClick()">点击按钮</button>
  <label for="email">邮箱:</label>
  <input type="email" id="email" required>
</div>`;
    setHtmlInput(exampleHtml);
  }, []);

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">HTML 到 React TSX 转换器</h1>
        <p className="text-slate-300 mb-4">
          将原生 HTML 代码转换为 React TSX 语法，支持属性名转换、自闭合标签、内联样式对象化等功能。
        </p>
        
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={loadExample}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
          >
            📝 加载示例
          </button>
          <button
            onClick={handleClear}
            className="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors text-sm"
          >
            🗑️ 清空
          </button>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* HTML 输入区域 */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">HTML 输入</h2>
            <span className="text-sm text-slate-400">
              {htmlInput.length} 字符
            </span>
          </div>
          
          <textarea
            value={htmlInput}
            onChange={(e) => setHtmlInput(e.target.value)}
            onInput={handleConvert}
            placeholder="在此粘贴您的 HTML 代码..."
            className="w-full h-96 bg-slate-900/50 border border-slate-600/50 rounded-lg p-4 text-slate-100 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
          />
          
          <div className="mt-4 flex gap-3">
            <button
              onClick={handleConvert}
              disabled={!htmlInput.trim()}
              className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 text-sm font-medium"
            >
              🔄 转换
            </button>
          </div>
        </div>

        {/* TSX 输出区域 */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">TSX 输出</h2>
            <div className="flex items-center gap-3">
              {tsxOutput && (
                <span className="text-sm text-slate-400">
                  {tsxOutput.length} 字符
                </span>
              )}
              {validationResult.isValid ? (
                <span className="text-green-400 text-sm">✅ 有效</span>
              ) : (
                <span className="text-red-400 text-sm">❌ 有错误</span>
              )}
            </div>
          </div>
          
          <div className="relative">
            <textarea
              value={tsxOutput}
              readOnly
              placeholder="转换后的 TSX 代码将显示在这里..."
              className="w-full h-96 bg-slate-900/50 border border-slate-600/50 rounded-lg p-4 text-slate-100 font-mono text-sm resize-none focus:outline-none"
            />
            
            {tsxOutput && (
              <button
                onClick={handleCopy}
                className={`absolute top-3 right-3 px-3 py-1 rounded text-xs font-medium transition-all duration-200 ${
                  copySuccess 
                    ? 'bg-green-600 text-white' 
                    : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                }`}
              >
                {copySuccess ? '✅ 已复制' : '📋 复制'}
              </button>
            )}
          </div>
          
          {tsxOutput && (
            <div className="mt-4 flex gap-3">
              <button
                onClick={handleCopy}
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-lg transition-all duration-200 text-sm font-medium"
              >
                📋 复制到剪贴板
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 验证错误显示 */}
      {!validationResult.isValid && validationResult.errors.length > 0 && (
        <div className="mt-6 bg-red-900/20 border border-red-500/30 rounded-xl p-4">
          <h3 className="text-red-400 font-semibold mb-2">⚠️ 验证错误</h3>
          <ul className="text-red-300 text-sm space-y-1">
            {validationResult.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* 功能说明 */}
      <div className="mt-8 bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">🔧 转换功能</h3>
        <div className="grid md:grid-cols-2 gap-6 text-sm">
          <div>
            <h4 className="text-cyan-400 font-medium mb-2">属性名转换</h4>
            <ul className="text-slate-300 space-y-1">
              <li>• <code className="bg-slate-700 px-1 rounded">class</code> → <code className="bg-slate-700 px-1 rounded">className</code></li>
              <li>• <code className="bg-slate-700 px-1 rounded">for</code> → <code className="bg-slate-700 px-1 rounded">htmlFor</code></li>
              <li>• <code className="bg-slate-700 px-1 rounded">tabindex</code> → <code className="bg-slate-700 px-1 rounded">tabIndex</code></li>
              <li>• 以及更多常用属性...</li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-cyan-400 font-medium mb-2">标签处理</h4>
            <ul className="text-slate-300 space-y-1">
              <li>• 自闭合标签格式化 (<code className="bg-slate-700 px-1 rounded">&lt;img /&gt;</code>)</li>
              <li>• 事件处理器转换 (<code className="bg-slate-700 px-1 rounded">onclick</code> → <code className="bg-slate-700 px-1 rounded">onClick</code>)</li>
              <li>• 内联样式对象化</li>
              <li>• HTML 注释转 JSX 注释</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
