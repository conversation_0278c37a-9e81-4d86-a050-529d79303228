'use client';

import { useState, useRef, useEffect } from 'react';
import html2canvas from 'html2canvas';

// 预设配置
const presets = {
  cursor: {
    seriesBadge: "CURSOR SERIES",
    mainTitle: "告别请求数焦虑",
    subtitle: "Cursor 效率优化秘籍",
    bgImage: "/assets/series/cursor/cursor.png"
  },
  "ai-tools": {
    seriesBadge: "AI TOOLS",
    mainTitle: "AI 工具大揭秘",
    subtitle: "提升工作效率的神器",
    bgImage: "/assets/series/ai-tools/ai-tools.png"
  },
  programming: {
    seriesBadge: "PROGRAMMING",
    mainTitle: "编程技巧分享",
    subtitle: "让代码更优雅",
    bgImage: "/assets/series/programming/code.png"
  },
  "tech-review": {
    seriesBadge: "TECH REVIEW",
    mainTitle: "科技产品评测",
    subtitle: "深度体验报告",
    bgImage: "/assets/series/tech-review/tech.png"
  }
};

export default function CoverTemplate() {
  const [seriesBadge, setSeriesBadge] = useState('CURSOR SERIES');
  const [mainTitle, setMainTitle] = useState('告别请求数焦虑');
  const [subtitle, setSubtitle] = useState('Cursor 效率优化秘籍');
  const [bgImage, setBgImage] = useState('/assets/series/cursor/cursor.png');
  const [isConfigOpen, setIsConfigOpen] = useState(true);
  const [statusMessage, setStatusMessage] = useState('');
  const [selectedPreset, setSelectedPreset] = useState('cursor');
  
  const headerImageRef = useRef<HTMLDivElement>(null);

  // 加载预设配置
  const loadPreset = () => {
    if (selectedPreset === 'custom') return;
    
    const preset = presets[selectedPreset as keyof typeof presets];
    if (preset) {
      setSeriesBadge(preset.seriesBadge);
      setMainTitle(preset.mainTitle);
      setSubtitle(preset.subtitle);
      setBgImage(preset.bgImage);
      showStatus(`已加载 ${selectedPreset} 预设`);
    }
  };

  // 监听预设选择变更
  useEffect(() => {
    loadPreset();
  }, [selectedPreset]);

  // 显示状态消息
  const showStatus = (message: string, duration = 3000) => {
    setStatusMessage(message);
    setTimeout(() => {
      setStatusMessage('');
    }, duration);
  };

  // 等待所有图片加载完成
  const waitForImages = (element: HTMLElement) => {
    return new Promise<void>((resolve) => {
      const images = element.querySelectorAll('img');
      if (images.length === 0) {
        resolve();
        return;
      }
      
      let loadedCount = 0;
      const totalImages = images.length;
      
      function checkComplete() {
        loadedCount++;
        if (loadedCount === totalImages) {
          resolve();
        }
      }
      
      images.forEach(img => {
        if (img.complete) {
          checkComplete();
        } else {
          img.onload = checkComplete;
          img.onerror = checkComplete; // 即使加载失败也继续
        }
      });
    });
  };

  // 复制到剪贴板
  const copyToClipboard = async () => {
    if (!headerImageRef.current) return;

    try {
      showStatus('正在生成图片...');
      const element = headerImageRef.current;
      
      // 等待所有图片加载完成
      await waitForImages(element);

      const canvas = await html2canvas(element, {
        width: 900,
        height: 383,
        scale: 2,
        backgroundColor: '#000000',
        useCORS: true,
        allowTaint: false,
        logging: false,
        imageTimeout: 15000,
        onclone: function(clonedDoc) {
          // 确保克隆的文档中的样式正确应用
          const clonedElement = clonedDoc.getElementById('headerImage');
          if (clonedElement) {
            clonedElement.style.transform = 'none';
            clonedElement.style.position = 'static';
            clonedElement.style.margin = '0';
            clonedElement.style.padding = '0';
          }
          
          // 确保图片正确显示 - 保持原始宽高比
          const clonedImg = clonedDoc.getElementById('cursorImage');
          if (clonedImg && clonedImg.getAttribute('src')) {
            clonedImg.style.display = 'block';
            clonedImg.style.maxWidth = '280px';
            clonedImg.style.maxHeight = '200px';
            clonedImg.style.width = 'auto';
            clonedImg.style.height = 'auto';
            clonedImg.style.objectFit = 'contain';
            clonedImg.style.objectPosition = 'center';
          }
        }
      });

      canvas.toBlob(async (blob: Blob | null) => {
        if (blob) {
          try {
            await navigator.clipboard.write([
              new ClipboardItem({ 'image/png': blob })
            ]);
            showStatus('✅ 图片已复制到剪贴板！');
          } catch (error) {
            showStatus('❌ 复制失败，请检查浏览器权限');
            console.error('复制失败:', error);
          }
        }
      });
    } catch (error) {
      showStatus('❌ 生成图片失败');
      console.error('生成图片失败:', error);
    }
  };

  // 下载图片
  const downloadImage = async () => {
    if (!headerImageRef.current) return;

    try {
      showStatus('正在生成图片...');
      const element = headerImageRef.current;
      
      // 等待所有图片加载完成
      await waitForImages(element);
      
      const canvas = await html2canvas(element, {
        width: 900,
        height: 383,
        scale: 2,
        backgroundColor: '#000000',
        useCORS: true,
        allowTaint: false,
        logging: false,
        imageTimeout: 15000
      });
      
      // 创建下载链接
      const link = document.createElement('a');
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      link.download = `微信首图_${timestamp}.png`;
      
      // 转换为blob以获得更好的兼容性
      canvas.toBlob(function(blob) {
        if (blob) {
          const url = URL.createObjectURL(blob);
          link.href = url;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          showStatus('✅ 图片下载成功！');
        } else {
          throw new Error('无法生成图片blob');
        }
      }, 'image/png', 0.95);
      
    } catch (error) {
      showStatus('❌ 下载失败，请稍后重试');
      console.error('下载失败:', error);
    }
  };

  return (
    <div className="cover-template relative">
      {/* 状态提示 */}
      {statusMessage && (
        <div className="fixed top-4 right-4 bg-slate-800 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          {statusMessage}
        </div>
      )}

      {/* 配置面板 */}
      <div className="mb-6">
        <button
          onClick={() => setIsConfigOpen(!isConfigOpen)}
          className="flex items-center gap-2 mb-4 px-4 py-2 bg-slate-700 text-white rounded-lg hover:bg-slate-600 transition-colors"
        >
          ⚙️ 配置
        </button>

        {isConfigOpen && (
          <div className="bg-slate-700/50 rounded-lg p-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  📋 选择预设系列:
                </label>
                <select
                  value={selectedPreset}
                  onChange={(e) => setSelectedPreset(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                >
                  <option value="cursor">Cursor 系列</option>
                  <option value="ai-tools">AI 工具系列</option>
                  <option value="programming">编程技巧系列</option>
                  <option value="tech-review">科技评测系列</option>
                  <option value="custom">自定义</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  🏷️ 系列标识:
                </label>
                <input
                  type="text"
                  value={seriesBadge}
                  onChange={(e) => setSeriesBadge(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  📝 主标题:
                </label>
                <input
                  type="text"
                  value={mainTitle}
                  onChange={(e) => setMainTitle(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  📄 副标题:
                </label>
                <input
                  type="text"
                  value={subtitle}
                  onChange={(e) => setSubtitle(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  🖼️ 背景图片路径:
                </label>
                <input
                  type="text"
                  value={bgImage}
                  onChange={(e) => setBgImage(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                />
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={copyToClipboard}
                className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200"
              >
                📋 复制到剪贴板
              </button>
              <button
                onClick={downloadImage}
                className="px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-200"
              >
                📥 下载图片 (PNG)
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 封面预览 - 遵循微信首图模板的样式 */}
      <div 
        id="headerImage"
        ref={headerImageRef}
        className="mx-auto"
        style={{
          width: '900px',
          height: '383px',
          background: 'linear-gradient(135deg, #000000 0%, #111111 100%)',
          borderRadius: '12px',
          overflow: 'hidden',
          position: 'relative',
          display: 'flex'
        }}
      >
        {/* 左侧图片展示区 */}
        <div style={{ 
          width: '40%', 
          height: '100%', 
          position: 'relative',
          background: 'linear-gradient(135deg, #000000 0%, #0a0a0a 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {/* 左侧网格背景 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px)
            `,
            backgroundSize: '30px 30px',
            zIndex: 1
          }}></div>
          
          {/* 左侧装饰元素 */}
          <div style={{
            position: 'absolute',
            top: '25px',
            left: '25px',
            width: '60px',
            height: '4px',
            background: 'linear-gradient(90deg, #ff6bb3, #00d4ff)',
            borderRadius: '2px',
            zIndex: 4
          }}></div>
          
          {/* cursor图片 */}
          <img 
            id="cursorImage"
            src={bgImage}
            alt="主题图片"
            style={{
              maxWidth: '280px',
              maxHeight: '200px',
              width: 'auto',
              height: 'auto',
              zIndex: 3,
              objectFit: 'contain',
              objectPosition: 'center'
            }}
          />
        </div>
        
        {/* 分割线 */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: '40%',
          width: '2px',
          height: '100%',
          background: `linear-gradient(180deg, 
            transparent 0%, 
            rgba(255, 107, 179, 0.3) 20%, 
            rgba(0, 212, 255, 0.3) 50%, 
            rgba(57, 255, 20, 0.3) 80%, 
            transparent 100%)`,
          zIndex: 2
        }}></div>
        
        {/* 右侧内容区 */}
        <div style={{ 
          width: '60%', 
          height: '100%', 
          position: 'relative',
          background: 'linear-gradient(135deg, #111111 0%, #000000 100%)',
          padding: '40px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center'
        }}>
          {/* 右侧网格背景 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundImage: `
              linear-gradient(rgba(255, 107, 179, 0.03) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 179, 0.03) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px',
            zIndex: 1
          }}></div>
          
          {/* 头像 */}
          <img 
            className="brand-avatar"
            src="/assets/avatar/avatar.jpg"
            alt="头像"
            style={{
              position: 'absolute',
              top: '25px',
              right: '25px',
              width: '45px',
              height: '45px',
              borderRadius: '50%',
              objectFit: 'cover',
              border: '2px solid rgba(255, 107, 179, 0.6)',
              boxShadow: '0 0 15px rgba(255, 107, 179, 0.3)',
              zIndex: 10
            }}
          />
          
          {/* 内容 */}
          <div style={{
            position: 'relative',
            zIndex: 5,
            maxWidth: '100%'
          }}>
            {/* 系列标识 */}
            <div style={{
              display: 'inline-block',
              padding: '5px 14px',
              background: 'linear-gradient(135deg, rgba(255, 107, 179, 0.15), rgba(0, 212, 255, 0.15))',
              border: '1px solid rgba(255, 107, 179, 0.4)',
              borderRadius: '20px',
              color: '#ff6bb3',
              fontSize: '10px',
              fontWeight: 700,
              letterSpacing: '1.5px',
              marginBottom: '15px',
              textTransform: 'uppercase',
              backdropFilter: 'blur(10px)',
              width: 'fit-content'
            }}>
              {seriesBadge}
            </div>
            
            {/* 主标题 */}
            <h1 style={{
              fontSize: '56px',
              fontWeight: 900,
              color: '#ffffff',
              marginBottom: '10px',
              letterSpacing: '-2px',
              lineHeight: 0.9,
              textShadow: '0 0 20px rgba(255, 107, 179, 0.3)'
            }}>
              {mainTitle}
            </h1>
            
            {/* 副标题 */}
            <p style={{
              fontSize: '18px',
              fontWeight: 400,
              color: 'rgba(255, 255, 255, 0.8)',
              marginBottom: '25px',
              letterSpacing: '0.5px',
              lineHeight: 1.4
            }}>
              {subtitle}
            </p>
            
            {/* 装饰线 */}
            <div style={{
              width: '80px',
              height: '3px',
              background: 'linear-gradient(90deg, #ff6bb3, #00d4ff, #39ff14)',
              borderRadius: '2px',
              marginTop: '15px'
            }}></div>
          </div>
          
          {/* 品牌信息 */}
          <div style={{
            position: 'absolute',
            bottom: '25px',
            right: '25px',
            textAlign: 'right',
            zIndex: 10
          }}>
            <div style={{
              fontSize: '16px',
              fontWeight: 700,
              color: '#ffffff',
              marginBottom: '2px',
              letterSpacing: '0.8px'
            }}>
              硅基茶馆2077
            </div>
            <div style={{
              fontSize: '10px',
              color: 'rgba(255, 255, 255, 0.5)',
              letterSpacing: '1.2px',
              textTransform: 'uppercase'
            }}>
              Silicon Tea House
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 