"use client";

import React, { useState, useRef, useEffect } from "react";
import html2canvas from "html2canvas";

// 预设配置
const coverPresets = {
  cursor: {
    seriesBadge: "CURSOR SERIES",
    mainTitle: "告别请求数焦虑",
    subtitle: "Cursor 效率优化秘籍",
    bgImage: "/assets/series/cursor/cursor.png",
  },
  "ai-tools": {
    seriesBadge: "AI TOOLS",
    mainTitle: "AI 工具大揭秘",
    subtitle: "提升工作效率的神器",
    bgImage: "/assets/series/ai-tools/ai-tools.png",
  },
  programming: {
    seriesBadge: "PROGRAMMING",
    mainTitle: "编程技巧分享",
    subtitle: "让代码更优雅",
    bgImage: "/assets/series/programming/code.png",
  },
  "tech-review": {
    seriesBadge: "TECH REVIEW",
    mainTitle: "科技产品评测",
    subtitle: "深度体验报告",
    bgImage: "/assets/series/tech-review/tech.png",
  },
};

interface CoverGeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTitle?: string;
  initialSubtitle?: string;
}

export default function CoverGeneratorModal({
  isOpen,
  onClose,
  initialTitle = "文章标题",
  initialSubtitle = "文章副标题",
}: CoverGeneratorModalProps) {
  const [seriesBadge, setSeriesBadge] = useState("CURSOR SERIES");
  const [mainTitle, setMainTitle] = useState(initialTitle);
  const [subtitle, setSubtitle] = useState(initialSubtitle);
  const [bgImage, setBgImage] = useState("/assets/series/cursor/cursor.png");
  const [selectedPreset, setSelectedPreset] = useState("cursor");
  const [coverStatus, setCoverStatus] = useState("");
  const [dynamicFontSize, setDynamicFontSize] = useState("36px");

  const headerImageRef = useRef<HTMLDivElement>(null);

  // 动态计算字体大小
  const calculateFontSize = (text: string): string => {
    const length = text.length;
    if (length <= 8) {
      return "clamp(28px, 4vw, 36px)";
    } else if (length <= 15) {
      return "clamp(24px, 3.5vw, 32px)";
    } else if (length <= 25) {
      return "clamp(20px, 3vw, 28px)";
    } else {
      return "clamp(18px, 2.5vw, 24px)";
    }
  };

  // 当初始值改变时更新状态
  useEffect(() => {
    setMainTitle(initialTitle);
    setSubtitle(initialSubtitle);
  }, [initialTitle, initialSubtitle]);

  // 监听主标题变化，动态调整字体大小
  useEffect(() => {
    setDynamicFontSize(calculateFontSize(mainTitle));
  }, [mainTitle]);

  // 显示状态消息
  const showCoverStatus = (message: string, duration = 3000) => {
    setCoverStatus(message);
    setTimeout(() => {
      setCoverStatus("");
    }, duration);
  };

  // 加载预设配置
  const loadPreset = () => {
    if (selectedPreset === "custom") return;

    const preset = coverPresets[selectedPreset as keyof typeof coverPresets];
    if (preset) {
      setSeriesBadge(preset.seriesBadge);
      setMainTitle(preset.mainTitle);
      setSubtitle(preset.subtitle);
      setBgImage(preset.bgImage);
      showCoverStatus(`已加载 ${selectedPreset} 预设`);
    }
  };

  // 等待所有图片加载完成
  const waitForImages = (element: HTMLElement) => {
    return new Promise<void>((resolve) => {
      const images = element.querySelectorAll("img");
      if (images.length === 0) {
        resolve();
        return;
      }

      let loadedCount = 0;
      const totalImages = images.length;

      function checkComplete() {
        loadedCount++;
        if (loadedCount === totalImages) {
          resolve();
        }
      }

      images.forEach((img) => {
        if (img.complete) {
          checkComplete();
        } else {
          img.onload = checkComplete;
          img.onerror = checkComplete;
        }
      });
    });
  };

  // 复制封面到剪贴板
  const copyCoverToClipboard = async () => {
    if (!headerImageRef.current) return;

    try {
      showCoverStatus("正在生成封面图片...");
      const element = headerImageRef.current;

      // 等待所有图片加载完成
      await waitForImages(element);

      const canvas = await html2canvas(element, {
        width: 900,
        height: 383,
        scale: 2,
        backgroundColor: "#000000",
        useCORS: true,
        allowTaint: false,
        logging: false,
        imageTimeout: 15000,
        onclone: function (clonedDoc) {
          const clonedElement = clonedDoc.getElementById("headerImage");
          if (clonedElement) {
            clonedElement.style.transform = "none";
            clonedElement.style.position = "static";
            clonedElement.style.margin = "0";
            clonedElement.style.padding = "0";
          }

          const clonedImg = clonedDoc.getElementById("coverBgImage");
          if (clonedImg && clonedImg.getAttribute("src")) {
            clonedImg.style.display = "block";
            clonedImg.style.maxWidth = "280px";
            clonedImg.style.maxHeight = "200px";
            clonedImg.style.width = "auto";
            clonedImg.style.height = "auto";
            clonedImg.style.objectFit = "contain";
            clonedImg.style.objectPosition = "center";
          }

          // 修复系列标签容器的样式
          const seriesContainers = clonedDoc.querySelectorAll(
            "[data-series-container]"
          );
          seriesContainers.forEach((container) => {
            const containerEl = container as HTMLElement;
            containerEl.style.display = "flex";
            containerEl.style.justifyContent = "flex-start";
            containerEl.style.alignItems = "center"; // 修复：改为center实现垂直居中
            containerEl.style.marginBottom = "15px";
            containerEl.style.width = "100%";
          });

          // 修复系列标签的样式
          const seriesBadges = clonedDoc.querySelectorAll(
            "[data-series-badge]"
          );
          seriesBadges.forEach((badge) => {
            const badgeEl = badge as HTMLElement;
            badgeEl.style.display = "flex"; // 修复：改为flex布局
            badgeEl.style.alignItems = "center"; // 确保文字垂直居中
            badgeEl.style.justifyContent = "center"; // 确保文字水平居中
            badgeEl.style.padding = "5px 14px";
            badgeEl.style.background =
              "linear-gradient(135deg, rgba(255, 107, 179, 0.15), rgba(0, 212, 255, 0.15))";
            badgeEl.style.border = "1px solid rgba(255, 107, 179, 0.4)";
            badgeEl.style.borderRadius = "20px";
            badgeEl.style.color = "#ff6bb3";
            badgeEl.style.fontSize = "10px";
            badgeEl.style.fontWeight = "700";
            badgeEl.style.letterSpacing = "1.5px";
            badgeEl.style.textTransform = "uppercase";
            badgeEl.style.textAlign = "center";
            badgeEl.style.whiteSpace = "nowrap";
            badgeEl.style.lineHeight = "1"; // 确保行高不影响垂直对齐
            badgeEl.style.backdropFilter = "none"; // html2canvas 不支持 backdrop-filter
          });
        },
      });

      canvas.toBlob(async (blob: Blob | null) => {
        if (blob) {
          try {
            await navigator.clipboard.write([
              new ClipboardItem({ "image/png": blob }),
            ]);
            showCoverStatus("✅ 封面图片已复制到剪贴板！");
          } catch (error) {
            showCoverStatus("❌ 复制失败，请检查浏览器权限");
            console.error("复制失败:", error);
          }
        }
      });
    } catch (error) {
      showCoverStatus("❌ 生成封面图片失败");
      console.error("生成封面图片失败:", error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-2xl max-w-7xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto shadow-2xl border border-slate-700/50">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700/50 bg-gradient-to-r from-slate-800/50 to-slate-700/50">
          <h2
            className="text-xl sm:text-2xl font-bold"
            style={{ color: "#ff6bb3", margin: 0 }}
          >
            封面生成器
          </h2>

          <button
            onClick={onClose}
            className="text-slate-400 hover:text-white text-2xl w-10 h-10 flex items-center justify-center rounded-lg hover:bg-slate-700/50 transition-all"
          >
            ×
          </button>
        </div>

        {/* 状态提示 */}
        {coverStatus && (
          <div className="mx-6 mt-4 bg-slate-800 text-white px-4 py-2 rounded-lg">
            {coverStatus}
          </div>
        )}

        <div className="p-4 sm:p-6 bg-gradient-to-b from-slate-800/20 to-slate-900/20">
          {/* 封面预览区域 */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">封面预览</h3>
              <div className="text-sm text-slate-400">
                标题长度: {mainTitle.length} 字符
                <span
                  className={`ml-2 px-2 py-1 rounded text-xs ${
                    mainTitle.length <= 8
                      ? "bg-green-500/20 text-green-400"
                      : mainTitle.length <= 15
                      ? "bg-yellow-500/20 text-yellow-400"
                      : mainTitle.length <= 25
                      ? "bg-orange-500/20 text-orange-400"
                      : "bg-red-500/20 text-red-400"
                  }`}
                >
                  {mainTitle.length <= 8
                    ? "大字体"
                    : mainTitle.length <= 15
                    ? "中字体"
                    : mainTitle.length <= 25
                    ? "小字体"
                    : "超小字体"}
                </span>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="w-full max-w-5xl">
                <div
                  ref={headerImageRef}
                  id="headerImage"
                  className="mx-auto"
                  style={{
                    width: "100%",
                    maxWidth: "900px",
                    aspectRatio: "900/383",
                    background:
                      "linear-gradient(135deg, #000000 0%, #111111 100%)",
                    position: "relative",
                    display: "flex",
                  }}
                >
                  {/* 左侧图片展示区 */}
                  <div
                    style={{
                      width: "40%",
                      height: "100%",
                      position: "relative",
                      background:
                        "linear-gradient(135deg, #000000 0%, #0a0a0a 100%)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {/* 左侧网格背景 */}
                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        backgroundImage: `
                        linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px)
                      `,
                        backgroundSize: "30px 30px",
                        zIndex: 1,
                      }}
                    ></div>

                    {/* 左侧装饰元素 */}
                    <div
                      style={{
                        position: "absolute",
                        top: "25px",
                        left: "25px",
                        width: "60px",
                        height: "4px",
                        background: "linear-gradient(90deg, #ff6bb3, #00d4ff)",
                        borderRadius: "2px",
                        zIndex: 4,
                      }}
                    ></div>

                    {/* 背景图片 */}
                    <img
                      id="coverBgImage"
                      src={bgImage}
                      alt="主题图片"
                      style={{
                        maxWidth: "280px",
                        maxHeight: "200px",
                        width: "auto",
                        height: "auto",
                        zIndex: 3,
                        objectFit: "contain",
                        objectPosition: "center",
                      }}
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = "none";
                      }}
                    />
                  </div>

                  {/* 分割线 */}
                  <div
                    style={{
                      position: "absolute",
                      top: 0,
                      left: "40%",
                      width: "2px",
                      height: "100%",
                      background: `linear-gradient(180deg,
                      transparent 0%,
                      rgba(255, 107, 179, 0.3) 20%,
                      rgba(0, 212, 255, 0.3) 50%,
                      rgba(57, 255, 20, 0.3) 80%,
                      transparent 100%)`,
                      zIndex: 2,
                    }}
                  ></div>

                  {/* 右侧内容区 */}
                  <div
                    style={{
                      width: "60%",
                      height: "100%",
                      position: "relative",
                      background:
                        "linear-gradient(135deg, #111111 0%, #000000 100%)",
                      padding: "40px",
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                    }}
                  >
                    {/* 右侧网格背景 */}
                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        backgroundImage: `
                        linear-gradient(rgba(255, 107, 179, 0.03) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(255, 107, 179, 0.03) 1px, transparent 1px)
                      `,
                        backgroundSize: "40px 40px",
                        zIndex: 1,
                      }}
                    ></div>

                    {/* 头像 */}
                    <img
                      src="/assets/avatar/avatar.jpg"
                      alt="头像"
                      style={{
                        position: "absolute",
                        top: "25px",
                        right: "25px",
                        width: "45px",
                        height: "45px",
                        borderRadius: "50%",
                        objectFit: "cover",
                        border: "2px solid rgba(255, 107, 179, 0.6)",
                        boxShadow: "0 0 15px rgba(255, 107, 179, 0.3)",
                        zIndex: 10,
                      }}
                    />

                    {/* 内容 */}
                    <div
                      style={{
                        position: "relative",
                        zIndex: 5,
                        maxWidth: "100%",
                      }}
                    >
                      {/* 系列标识 */}
                      <div
                        data-series-container
                        style={{
                          display: "flex",
                          justifyContent: "flex-start",
                          alignItems: "center",
                          marginBottom: "15px",
                        }}
                      >
                        <div
                          data-series-badge
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            padding: "5px 14px",
                            background:
                              "linear-gradient(135deg, rgba(255, 107, 179, 0.15), rgba(0, 212, 255, 0.15))",
                            border: "1px solid rgba(255, 107, 179, 0.4)",
                            borderRadius: "20px",
                            color: "#ff6bb3",
                            fontSize: "10px",
                            fontWeight: 700,
                            letterSpacing: "1.5px",
                            textTransform: "uppercase",
                            backdropFilter: "blur(10px)",
                            textAlign: "center",
                            whiteSpace: "nowrap",
                            lineHeight: 1,
                          }}
                        >
                          {seriesBadge}
                        </div>
                      </div>

                      {/* 主标题 */}
                      <h1
                        style={{
                          fontSize: dynamicFontSize,
                          fontWeight: 750,
                          color: "#ffffff",
                          marginBottom: "10px",
                          letterSpacing: "-1px",
                          lineHeight: 1.1,
                          textShadow: "0 0 20px rgba(255, 107, 179, 0.3)",
                          wordBreak: "break-word",
                          hyphens: "auto",
                        }}
                      >
                        {mainTitle}
                      </h1>

                      {/* 副标题 */}
                      <p
                        style={{
                          fontSize: "clamp(14px, 2vw, 18px)",
                          fontWeight: 400,
                          color: "rgba(255, 255, 255, 0.8)",
                          marginBottom: "25px",
                          letterSpacing: "0.5px",
                          lineHeight: 1.4,
                          wordBreak: "break-word",
                        }}
                      >
                        {subtitle}
                      </p>

                      {/* 装饰线 */}
                      <div
                        style={{
                          width: "80px",
                          height: "3px",
                          background:
                            "linear-gradient(90deg, #ff6bb3, #00d4ff, #39ff14)",
                          borderRadius: "2px",
                          marginBottom: "15px",
                        }}
                      ></div>

                      {/* 品牌信息 */}
                      <div
                        style={{
                          fontSize: "14px",
                          color: "rgba(255, 255, 255, 0.6)",
                          fontWeight: 500,
                          letterSpacing: "1px",
                        }}
                      >
                        硅基茶馆2077
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mb-8">
            <button
              onClick={copyCoverToClipboard}
              className="px-6 py-3 bg-gradient-to-r from-pink-500 to-cyan-500 text-white font-bold rounded-lg hover:from-pink-600 hover:to-cyan-600 transition-all"
            >
              {coverStatus || "📋 复制封面图片"}
            </button>
            <button
              onClick={onClose}
              className="px-6 py-3 bg-slate-700 text-white font-bold rounded-lg hover:bg-slate-600 transition-all"
            >
              ❌ 关闭
            </button>
          </div>

          {/* 配置区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* 左侧配置组 */}
            <div className="space-y-4">
              {/* 预设选择卡片 */}
              <div className="bg-slate-800/50 p-5 rounded-xl border border-slate-700/50 backdrop-blur-sm">
                <label className="flex items-center gap-2 text-sm font-medium text-slate-300 mb-3">
                  <span className="text-cyan-400">⚙️</span>
                  选择预设模板
                </label>
                <select
                  value={selectedPreset}
                  onChange={(e) => setSelectedPreset(e.target.value)}
                  className="w-full p-3 bg-slate-700/80 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-all"
                >
                  <option value="cursor">🎯 Cursor 系列</option>
                  <option value="ai-tools">🤖 AI 工具</option>
                  <option value="programming">💻 编程技巧</option>
                  <option value="tech-review">📱 科技评测</option>
                  <option value="custom">✨ 自定义</option>
                </select>
                <button
                  onClick={loadPreset}
                  className="mt-3 w-full px-4 py-3 bg-gradient-to-r from-cyan-600 to-blue-600 text-white rounded-lg hover:from-cyan-700 hover:to-blue-700 transition-all font-medium shadow-lg"
                >
                  🔄 加载预设
                </button>
              </div>

              {/* 系列标签和主标题 */}
              <div className="bg-slate-800/50 p-5 rounded-xl border border-slate-700/50 backdrop-blur-sm">
                <label className="flex items-center gap-2 text-sm font-medium text-slate-300 mb-3">
                  <span className="text-pink-400">🏷️</span>
                  系列标签
                </label>
                <input
                  type="text"
                  value={seriesBadge}
                  onChange={(e) => setSeriesBadge(e.target.value)}
                  className="w-full p-3 bg-slate-700/80 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-all mb-4"
                  placeholder="例如：CURSOR SERIES"
                />

                <label className="flex items-center gap-2 text-sm font-medium text-slate-300 mb-3">
                  <span className="text-yellow-400">📝</span>
                  主标题
                </label>
                <input
                  type="text"
                  value={mainTitle}
                  onChange={(e) => setMainTitle(e.target.value)}
                  className="w-full p-3 bg-slate-700/80 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all"
                  placeholder="输入文章主标题"
                />
              </div>
            </div>

            {/* 右侧配置组 */}
            <div className="space-y-4">
              {/* 副标题 */}
              <div className="bg-slate-800/50 p-5 rounded-xl border border-slate-700/50 backdrop-blur-sm">
                <label className="flex items-center gap-2 text-sm font-medium text-slate-300 mb-3">
                  <span className="text-green-400">💬</span>
                  副标题
                </label>
                <input
                  type="text"
                  value={subtitle}
                  onChange={(e) => setSubtitle(e.target.value)}
                  className="w-full p-3 bg-slate-700/80 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                  placeholder="输入副标题或描述"
                />
              </div>

              {/* 背景图片 */}
              <div className="bg-slate-800/50 p-5 rounded-xl border border-slate-700/50 backdrop-blur-sm">
                <label className="flex items-center gap-2 text-sm font-medium text-slate-300 mb-3">
                  <span className="text-purple-400">🖼️</span>
                  背景图片路径
                </label>
                <input
                  type="text"
                  value={bgImage}
                  onChange={(e) => setBgImage(e.target.value)}
                  className="w-full p-3 bg-slate-700/80 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all"
                  placeholder="/assets/series/cursor/cursor.png"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
