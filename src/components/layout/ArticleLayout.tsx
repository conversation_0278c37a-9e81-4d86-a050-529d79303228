"use client";

import React, { useState } from "react";
import "@/styles/blogStyles.scss";
import "@/styles/wechat-article.scss";
import CoverGeneratorModal from "./CoverGeneratorModal";
import ShadcnHeader from "./ShadcnHeader";
import { Button } from '@/components/ui/button';
import { Copy, Palette, Eye, EyeOff } from 'lucide-react';

export interface ArticleMetadata {
  title: string;
  date: string;
  excerpt?: string;
}

interface ArticleLayoutProps {
  children: React.ReactNode;
  metadata?: ArticleMetadata;
}

export default function ArticleLayout({
  children,
  metadata,
}: ArticleLayoutProps) {
  const [copySuccess, setCopySuccess] = useState("");
  const [showPhoneFrame, setShowPhoneFrame] = useState(true);
  const [showCoverModal, setShowCoverModal] = useState(false);

  // 处理图片路径，将本地路径转换为绝对路径
  const processImagePaths = (html: string): string => {
    return html
      .replace(/src="\/posts\//g, `src="${window.location.origin}/posts/`)
      .replace(/src="\/assets\//g, `src="${window.location.origin}/assets/`);
  };

  // 获取内联样式
  const getInlineStyles = (): string => {
    const styles = `
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333; background-color: #fff; padding: 0; margin: 0; word-wrap: break-word; word-break: break-all; }
        .article-content { font-size: 16px; line-height: 1.8; }
        h1 { font-size: 22px; font-weight: bold; color: #333; text-align: center; margin-bottom: 20px; line-height: 1.4; padding: 20px 10px; background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 179, 0.05) 100%); border: 1px solid rgba(0, 212, 255, 0.2); border-radius: 12px; }
        h2 { font-size: 20px; font-weight: bold; color: #333; margin: 30px 0 15px 0; padding: 12px 16px; border-bottom: 2px solid #00d4ff; border-left: 4px solid #ff6bb3; background: linear-gradient(90deg, rgba(255, 107, 179, 0.08) 0%, transparent 50%); border-radius: 0 8px 8px 0; }
        h3 { font-size: 18px; font-weight: bold; color: #333; margin: 25px 0 12px 0; padding: 8px 0 8px 16px; border-left: 4px solid #ff6bb3; background: linear-gradient(90deg, rgba(255, 107, 179, 0.05) 0%, transparent 30%); border-radius: 0 6px 6px 0; }
        h4 { font-size: 16px; font-weight: bold; color: #555; margin: 20px 0 10px 0; }
        p { margin-bottom: 16px; text-align: justify; color: #333; }
        ul, ol { margin: 16px 0; padding-left: 20px; }
        li { margin-bottom: 8px; line-height: 1.6; }
        strong { color: #ff6bb3; font-weight: bold; }
        a { color: #00d4ff; text-decoration: none; word-break: break-all; }
        img { max-width: 100%; height: auto; display: block; margin: 20px auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        pre { background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); border: 1px solid #00d4ff; border-radius: 8px; padding: 16px; margin: 16px 0; overflow-x: auto; font-size: 14px; line-height: 1.4; }
        code { font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace; background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-size: 14px; color: #ff6bb3; }
        pre code { background-color: transparent; padding: 0; color: #00ff88; display: block; }
        .divider { height: 2px; background: linear-gradient(90deg, transparent 0%, #00d4ff 20%, #ff6bb3 50%, #39ff14 80%, transparent 100%); margin: 40px 0; border: none; border-radius: 1px; }
        .highlight-box { background: linear-gradient(135deg, rgba(57, 255, 20, 0.08) 0%, rgba(57, 255, 20, 0.02) 100%); border-left: 4px solid #39ff14; border: 1px solid rgba(57, 255, 20, 0.3); padding: 16px 20px; margin: 20px 0; border-radius: 8px; }
        .highlight-box strong { color: #39ff14; }
        .section { margin-bottom: 30px; }
        .intro-text { font-size: 17px; color: #666; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 179, 0.05) 100%); border-radius: 12px; border: 1px solid rgba(0, 212, 255, 0.2); }
        .footer-note { text-align: center; font-size: 14px; color: #999; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; }
        .brand-header { text-align: center; margin-bottom: 30px; padding: 20px 0; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); border-radius: 12px; }
        .brand-name { font-size: 24px; font-weight: bold; color: #ff6bb3; margin-bottom: 8px; }
        .brand-subtitle { font-size: 14px; color: #00d4ff; }
        blockquote { margin: 20px 0; padding: 15px 20px; border-left: 4px solid #00d4ff; background: linear-gradient(90deg, rgba(0, 212, 255, 0.05) 0%, transparent 50%); border-radius: 0 8px 8px 0; font-style: italic; color: #555; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f5f5f5; font-weight: bold; }
      </style>
    `;
    return styles;
  };

  // 复制带样式的内容
  const copyArticleWithStyles = async () => {
    try {
      const contentElement = document.querySelector(".article-outer-container");
      if (!contentElement) return;

      // 克隆元素以避免修改原始DOM
      const clonedElement = contentElement.cloneNode(true) as HTMLElement;

      // 处理图片路径
      const processedHTML = processImagePaths(clonedElement.outerHTML);

      // 创建完整的HTML文档，包含样式
      const styledHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          ${getInlineStyles()}
        </head>
        <body>
          ${processedHTML}
        </body>
        </html>
      `;

      // 尝试复制富文本格式
      if (navigator.clipboard && window.ClipboardItem) {
        const blob = new Blob([styledHTML], { type: "text/html" });
        const clipboardItem = new ClipboardItem({
          "text/html": blob,
          "text/plain": new Blob([clonedElement.textContent || ""], {
            type: "text/plain",
          }),
        });

        await navigator.clipboard.write([clipboardItem]);
        setCopySuccess("✅ 富文本内容已复制！可直接粘贴到微信公众号");
      } else {
        // 降级到纯文本复制
        await navigator.clipboard.writeText(styledHTML);
        setCopySuccess("✅ HTML内容已复制！");
      }

      setTimeout(() => setCopySuccess(""), 3000);
    } catch (error) {
      console.error("复制失败:", error);
      setCopySuccess("❌ 复制失败，请重试");
      setTimeout(() => setCopySuccess(""), 2000);
    }
  };

  return (
    <div className="article-page min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 relative overflow-hidden">
      {/* 赛博朋克背景效果 */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-cyan-900/10 via-transparent to-pink-900/10"></div>
      <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(0,212,255,0.01)_50%,transparent_75%)] bg-[length:40px_40px] animate-pulse"></div>

      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(0,212,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(0,212,255,0.02)_1px,transparent_1px)] bg-[size:30px_30px]"></div>

      {/* shadcn/ui Header */}
      <ShadcnHeader />

      {/* 外部容器（会被复制，包含标题和复制按钮） */}
      <div className="article-outer-container pt-4 relative z-10 max-w-4xl w-3/4 mx-auto">
        {/* 文章标题 */}

        {/* 左右布局容器 */}
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="lg:w-1/2">
            <div className="sticky top-6">
              {/* 文章标题增强 */}
              {metadata && (
                <div className="relative mb-6">
                  <h1 className="article-title relative z-10 filter drop-shadow-[0_0_20px_rgba(0,212,255,0.3)] dark:text-white text-[#333]">
                    {metadata.title}
                  </h1>
                  {/* 发光效果 */}
                  <div className="absolute inset-0 text-xl font-bold text-cyan-400/10 blur-lg animate-pulse">
                    {metadata.title}
                  </div>
                </div>
              )}

              {/* 控制面板 */}
              <div className="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-md border border-cyan-500/20 rounded-xl p-6 shadow-2xl shadow-cyan-500/10">
                <div className="flex flex-col gap-4">
                  {/* 复制按钮 */}
                  <Button
                    onClick={copyArticleWithStyles}
                    className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white border-0 shadow-lg shadow-cyan-500/25 hover:shadow-cyan-500/40 transition-all duration-300 filter drop-shadow-[0_0_15px_rgba(0,212,255,0.3)]"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {copySuccess || "复制带样式的内容"}
                  </Button>

                  {/* 封面生成器按钮 */}
                  <Button
                    onClick={() => setShowCoverModal(true)}
                    variant="outline"
                    className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 border-pink-500/30 hover:bg-gradient-to-r hover:from-pink-500/20 hover:to-purple-500/20 text-pink-400 hover:text-pink-300 shadow-lg shadow-pink-500/25 hover:shadow-pink-500/40 transition-all duration-300 filter drop-shadow-[0_0_15px_rgba(255,107,179,0.2)]"
                  >
                    <Palette className="h-4 w-4 mr-2" />
                    生成封面图片
                  </Button>
                </div>
              </div>

              {/* 文章信息面板 */}
              {metadata && (
                <div className="bg-gradient-to-br from-slate-800/40 to-slate-700/20 backdrop-blur-md border border-pink-500/20 rounded-xl p-6 mt-4 shadow-xl shadow-pink-500/10">
                  <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-4 filter drop-shadow-[0_0_10px_rgba(0,212,255,0.3)]">
                    关于本文
                  </h3>
                  <p className="text-slate-300 mb-4 filter drop-shadow-[0_0_5px_rgba(255,107,179,0.2)]">
                    这是一篇关于 {metadata.title} 的文章，发布于 {metadata.date}。
                  </p>
                  <div className="border-t border-gradient-to-r from-cyan-500/30 to-pink-500/30 pt-4 mt-4">
                    <p className="text-slate-400 text-sm">
                      如需了解更多内容，请关注我们的微信公众号：
                    </p>
                    <p className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-purple-400 font-bold mt-2 filter drop-shadow-[0_0_8px_rgba(255,107,179,0.4)]">
                      硅基茶馆2077
                    </p>
                  </div>
                </div>
              )}

              {/* 手机框架切换按钮 */}
              <div className="flex justify-center mt-6 w-full">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-gradient-to-r from-purple-500/10 to-green-500/10 backdrop-blur-sm border-purple-500/30 hover:border-green-500/50 text-purple-400 hover:text-green-400 shadow-lg shadow-purple-500/20 hover:shadow-green-500/30 transition-all duration-300 filter drop-shadow-[0_0_10px_rgba(139,92,246,0.3)]"
                  onClick={() => setShowPhoneFrame(!showPhoneFrame)}
                >
                  {showPhoneFrame ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      隐藏手机框架
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      显示手机框架
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* 内部容器（手机壳） */}
          <div
            className={`article-container ${
              showPhoneFrame ? "phone-frame" : ""
            } lg:w-1/2 relative`}
          >
            {/* 文章容器背景效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-slate-800/20 to-slate-700/10 backdrop-blur-sm rounded-xl border border-slate-600/30 shadow-2xl shadow-cyan-500/5"></div>

            <div className="article-content relative z-10">{children}</div>

            {/* 页脚品牌信息 */}
            <div className="footer-note relative z-10">
              {/* 品牌头部（移至文章页脚） */}
              <div className="brand-header mt-8 relative">
                {/* 背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-slate-800/40 to-slate-700/20 backdrop-blur-md rounded-xl border border-cyan-500/20 shadow-2xl shadow-cyan-500/10"></div>

                <div className="relative z-10 p-6">
                  <div className="brand-name text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 filter drop-shadow-[0_0_20px_rgba(0,212,255,0.4)]">
                    硅基茶馆2077
                  </div>
                  <div className="brand-subtitle text-cyan-400/80 filter drop-shadow-[0_0_10px_rgba(0,212,255,0.3)]">
                    Silicon Based Teahouse · Future Tech Insights
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 封面生成器弹窗 */}
      <CoverGeneratorModal
        isOpen={showCoverModal}
        onClose={() => setShowCoverModal(false)}
        initialTitle={metadata?.title || "文章标题"}
        initialSubtitle={metadata?.excerpt || "文章副标题"}
      />
    </div>
  );
}
