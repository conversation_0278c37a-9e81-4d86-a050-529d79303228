"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { ThemeToggle } from "@/components/theme-toggle"
import { cn } from "@/lib/utils"
import React from "react"

export default function ShadcnHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        {/* Logo */}
        <Link 
          href="/" 
          className="flex items-center space-x-2 group"
        >
          <div className="text-xl md:text-2xl font-bold bg-gradient-to-r from-cyan-400 via-pink-400 to-purple-400 bg-clip-text text-transparent group-hover:from-pink-400 group-hover:via-cyan-400 group-hover:to-purple-400 transition-all duration-300">
            硅基茶馆2077
          </div>
          <div className="hidden md:block text-xs text-muted-foreground">
            Silicon Based Teahouse
          </div>
        </Link>

        {/* Navigation */}
        <div className="flex items-center space-x-4">
          <NavigationMenu className="hidden md:flex">
            <NavigationMenuList>
              <NavigationMenuItem>
                <NavigationMenuTrigger className="bg-transparent hover:bg-cyan-500/10 data-[state=open]:bg-cyan-500/10">
                  文章
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="bg-background/95 backdrop-blur-md border border-border/50 rounded-lg shadow-2xl shadow-cyan-500/10 p-1">
                    <ul className="grid gap-2 p-4 w-[450px] lg:w-[550px] lg:grid-cols-[1fr_1fr]">
                      <li className="row-span-4">
                        <NavigationMenuLink asChild>
                          <Link
                            className="flex h-full w-full select-none flex-col justify-end rounded-lg bg-gradient-to-br from-cyan-500/15 via-purple-500/10 to-pink-500/15 p-6 no-underline outline-none focus:shadow-lg hover:shadow-lg hover:shadow-cyan-500/20 transition-all duration-300 border border-cyan-500/20 hover:border-cyan-500/40"
                            href="/posts"
                          >
                            <div className="mb-3 mt-4 text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
                              文章列表
                            </div>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                              探索未来科技与思考的交汇点
                            </p>
                            <div className="mt-3 text-xs text-cyan-400/70">
                              查看所有文章 →
                            </div>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                      <ListItem href="/posts/new" title="创建文章" className="hover:bg-cyan-500/10 hover:border-cyan-500/30 border border-transparent rounded-lg">
                        开始您的创作之旅
                      </ListItem>
                      <ListItem href="/posts/2025-05-25" title="最新文章" className="hover:bg-pink-500/10 hover:border-pink-500/30 border border-transparent rounded-lg">
                        Cursor 请求优化指南
                      </ListItem>
                      <ListItem href="/posts/2025-06-01" title="热门文章" className="hover:bg-purple-500/10 hover:border-purple-500/30 border border-transparent rounded-lg">
                        Cursor 0.51 更新解读
                      </ListItem>
                      <ListItem href="/theme-test" title="主题测试" className="hover:bg-green-500/10 hover:border-green-500/30 border border-transparent rounded-lg">
                        测试页面展示
                      </ListItem>
                    </ul>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>
              
              <NavigationMenuItem>
                <NavigationMenuTrigger className="bg-transparent hover:bg-pink-500/10 data-[state=open]:bg-pink-500/10">
                  工具
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="bg-background/95 backdrop-blur-md border border-border/50 rounded-lg shadow-2xl shadow-pink-500/10 p-1">
                    <ul className="grid w-[500px] gap-2 p-4 md:w-[550px] md:grid-cols-2 lg:w-[600px]">
                      <ListItem href="/cover" title="封面生成器" className="hover:bg-pink-500/10 hover:border-pink-500/30 border border-transparent rounded-lg">
                        为您的文章创建精美封面
                      </ListItem>
                      <ListItem href="/tools/converter" title="格式转换" className="hover:bg-cyan-500/10 hover:border-cyan-500/30 border border-transparent rounded-lg">
                        Markdown 与 HTML 互转
                      </ListItem>
                      <ListItem href="/tools/preview" title="预览工具" className="hover:bg-purple-500/10 hover:border-purple-500/30 border border-transparent rounded-lg">
                        微信公众号样式预览
                      </ListItem>
                      <ListItem href="/tools/optimizer" title="图片优化" className="hover:bg-green-500/10 hover:border-green-500/30 border border-transparent rounded-lg">
                        压缩和优化图片资源
                      </ListItem>
                      <ListItem href="/tools/html-to-tsx" title="HTML转TSX" className="hover:bg-orange-500/10 hover:border-orange-500/30 border border-transparent rounded-lg">
                        HTML 代码转换工具
                      </ListItem>
                      <ListItem href="/tools/article-converter" title="文章转换" className="hover:bg-blue-500/10 hover:border-blue-500/30 border border-transparent rounded-lg">
                        文章格式转换器
                      </ListItem>
                    </ul>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          {/* Mobile Navigation */}
          <div className="flex md:hidden space-x-2">
            <Button variant="ghost" size="sm" asChild className="hover:bg-cyan-500/10">
              <Link href="/posts">文章</Link>
            </Button>
            <Button variant="ghost" size="sm" asChild className="hover:bg-pink-500/10">
              <Link href="/cover">工具</Link>
            </Button>
          </div>

          {/* Theme Toggle */}
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { title: string }
>(({ className, title, children, href, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          ref={ref}
          href={href || "#"}
          className={cn(
            "block select-none space-y-2 p-4 leading-none no-underline outline-none transition-all duration-300 group",
            className
          )}
          {...props}
        >
          <div className="text-sm font-semibold leading-none group-hover:text-foreground transition-colors">
            {title}
          </div>
          <p className="line-clamp-2 text-xs leading-relaxed text-muted-foreground group-hover:text-muted-foreground/80 transition-colors">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"
