'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Bold, 
  Italic, 
  Heading2, 
  Image as ImageIcon, 
  Code, 
  Minus,
  Square,
  AlertCircle 
} from 'lucide-react';

interface WeChatEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  editable?: boolean;
}

export function WeChatEditor({ content, onChange, editable = true }: WeChatEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
    ],
    content: content || {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [{ type: 'text', text: '开始编写您的文章...' }],
        },
      ],
    },
    editable,
    immediatelyRender: false, // 修复SSR问题
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 bg-slate-800 text-white rounded-lg border border-slate-600',
      },
    },
  });

  const addImage = () => {
    const url = window.prompt('请输入图片URL:');
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  };

  const addHighlightBox = () => {
    if (editor) {
      const content = window.prompt('请输入高亮内容:');
      if (content) {
        editor.chain().focus().insertContent(`
          <div class="highlight-box bg-cyan-900/30 border border-cyan-400 rounded-lg p-4 my-4">
            <div class="flex items-center gap-2 mb-2">
              <div class="w-2 h-2 bg-cyan-400 rounded-full"></div>
              <span class="text-cyan-400 font-semibold">提示</span>
            </div>
            <p class="text-slate-200">${content}</p>
          </div>
        `).run();
      }
    }
  };

  const addCodeBlock = () => {
    if (editor) {
      editor.chain().focus().toggleCodeBlock().run();
    }
  };

  const addSection = () => {
    if (editor) {
      editor.chain().focus().insertContent(`
        <div class="section bg-slate-700/50 rounded-lg p-6 my-6 border border-slate-600">
          <h3 class="text-xl font-semibold text-white mb-4">新章节</h3>
          <p class="text-slate-300">在这里添加章节内容...</p>
        </div>
      `).run();
    }
  };

  const addDivider = () => {
    if (editor) {
      editor.chain().focus().insertContent(`
        <div class="divider my-8 flex items-center">
          <div class="flex-1 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></div>
          <div class="mx-4 text-cyan-400">◆</div>
          <div class="flex-1 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></div>
        </div>
      `).run();
    }
  };

  if (!editor) {
    return <div className="p-4 text-center text-slate-400">加载编辑器中...</div>;
  }

  return (
    <div className="wechat-editor bg-slate-900 rounded-lg border border-slate-700">
      {/* 工具栏 */}
      <div className="border-b border-slate-700 p-3 flex gap-2 flex-wrap bg-slate-800 rounded-t-lg">
        {/* 基础格式 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`${
            editor.isActive('bold') ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
        >
          <Bold className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`${
            editor.isActive('italic') ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
        >
          <Italic className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 bg-slate-600" />

        {/* 标题 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={`${
            editor.isActive('heading', { level: 2 }) ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
        >
          <Heading2 className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 bg-slate-600" />

        {/* WeChat组件 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={addImage}
          className="text-slate-300 hover:text-white"
          title="插入图片"
        >
          <ImageIcon className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addHighlightBox}
          className="text-slate-300 hover:text-white"
          title="插入高亮框"
        >
          <AlertCircle className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addCodeBlock}
          className={`${
            editor.isActive('codeBlock') ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
          title="插入代码块"
        >
          <Code className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addSection}
          className="text-slate-300 hover:text-white"
          title="插入分区"
        >
          <Square className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addDivider}
          className="text-slate-300 hover:text-white"
          title="插入分割线"
        >
          <Minus className="w-4 h-4" />
        </Button>
      </div>

      {/* 编辑器内容 */}
      <EditorContent editor={editor} />
    </div>
  );
}
