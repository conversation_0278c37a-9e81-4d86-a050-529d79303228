'use client';

import React from 'react';
import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '@/app/posts/components';
import '@/styles/blogStyles.scss';
import '@/styles/wechat-article.scss';

interface ArticlePreviewProps {
  content: any; // TipTap JSON
  title?: string;
  className?: string;
}

export function ArticlePreview({ content, title, className = '' }: ArticlePreviewProps) {
  // 渲染单个节点
  const renderNode = (node: any, index: number): React.ReactNode => {
    if (!node || !node.type) return null;

    switch (node.type) {
      case 'paragraph':
        if (!node.content || node.content.length === 0) {
          return <p key={index}>&nbsp;</p>; // 空段落
        }
        return (
          <p key={index}>
            {node.content?.map((textNode: any, i: number) => 
              renderTextNode(textNode, i)
            )}
          </p>
        );
      
      case 'heading':
        const level = Math.min(Math.max(node.attrs?.level || 1, 1), 6);
        const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
        return (
          <HeadingTag key={index}>
            {node.content?.map((textNode: any, i: number) => 
              renderTextNode(textNode, i)
            )}
          </HeadingTag>
        );
      
      case 'image':
        return (
          <ImageContainer
            key={index}
            src={node.attrs?.src || ''}
            alt={node.attrs?.alt || '图片'}
            caption={node.attrs?.caption}
            width={node.attrs?.width}
            height={node.attrs?.height}
          />
        );
      
      case 'codeBlock':
        const codeContent = node.content?.map((textNode: any) => textNode.text).join('') || '';
        return (
          <CodeBlock 
            key={index} 
            language={node.attrs?.language || 'text'}
            title={node.attrs?.title}
            showLineNumbers={node.attrs?.showLineNumbers}
          >
            {codeContent}
          </CodeBlock>
        );
      
      case 'horizontalRule':
        return <Divider key={index} />;
      
      case 'blockquote':
        return (
          <blockquote key={index}>
            {node.content?.map((childNode: any, i: number) => 
              renderNode(childNode, i)
            )}
          </blockquote>
        );
      
      case 'bulletList':
        return (
          <ul key={index}>
            {node.content?.map((listItem: any, i: number) => (
              <li key={i}>
                {listItem.content?.map((childNode: any, j: number) => 
                  renderNode(childNode, j)
                )}
              </li>
            ))}
          </ul>
        );
      
      case 'orderedList':
        return (
          <ol key={index}>
            {node.content?.map((listItem: any, i: number) => (
              <li key={i}>
                {listItem.content?.map((childNode: any, j: number) => 
                  renderNode(childNode, j)
                )}
              </li>
            ))}
          </ol>
        );
      
      case 'listItem':
        // 这个case通常不会直接调用，因为在bulletList/orderedList中处理
        return (
          <li key={index}>
            {node.content?.map((childNode: any, i: number) => 
              renderNode(childNode, i)
            )}
          </li>
        );
      
      // 自定义WeChat组件节点
      case 'highlightBox':
        return (
          <HighlightBox
            key={index}
            type={node.attrs?.type || 'info'}
            title={node.attrs?.title}
          >
            {node.content?.map((childNode: any, i: number) => 
              renderNode(childNode, i)
            )}
          </HighlightBox>
        );
      
      case 'section':
        return (
          <Section key={index} className={node.attrs?.className}>
            {node.content?.map((childNode: any, i: number) => 
              renderNode(childNode, i)
            )}
          </Section>
        );
      
      default:
        // 对于未知节点类型，尝试渲染其内容
        if (node.content) {
          return (
            <div key={index} className="unknown-node">
              {node.content.map((childNode: any, i: number) => 
                renderNode(childNode, i)
              )}
            </div>
          );
        }
        return null;
    }
  };

  // 渲染文本节点（处理格式化）
  const renderTextNode = (textNode: any, index: number): React.ReactNode => {
    if (!textNode || textNode.type !== 'text') return null;
    
    let text = textNode.text || '';
    let element: React.ReactNode = text;
    
    // 应用文本标记
    if (textNode.marks) {
      textNode.marks.forEach((mark: any) => {
        switch (mark.type) {
          case 'bold':
            element = <strong key={`bold-${index}`}>{element}</strong>;
            break;
          case 'italic':
            element = <em key={`italic-${index}`}>{element}</em>;
            break;
          case 'code':
            element = <code key={`code-${index}`}>{element}</code>;
            break;
          case 'link':
            element = (
              <a 
                key={`link-${index}`} 
                href={mark.attrs?.href || '#'}
                target={mark.attrs?.target}
                rel={mark.attrs?.rel}
              >
                {element}
              </a>
            );
            break;
        }
      });
    }
    
    return <span key={index}>{element}</span>;
  };

  // 如果没有内容，显示默认提示
  if (!content || !content.content || content.content.length === 0) {
    return (
      <div className={`article-preview ${className}`}>
        <div className="article-container wechat-article-body">
          {title && <h1 className="article-title">{title}</h1>}
          <div className="article-content">
            <p className="text-slate-400 italic text-center py-8">
              暂无内容，请开始编写您的文章...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`article-preview ${className}`}>
      {/* 应用完整的WeChat样式容器 */}
      <div className="article-container wechat-article-body">
        {title && <h1 className="article-title">{title}</h1>}
        <div className="article-content">
          {content.content.map((node: any, index: number) => 
            renderNode(node, index)
          )}
        </div>
      </div>
    </div>
  );
}

export default ArticlePreview;
