'use client';

import { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface SimpleEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  editable?: boolean;
}

// 辅助函数：从TipTap JSON提取文本
const extractTextFromTipTap = (tiptapContent: any): string => {
  if (!tiptapContent || !tiptapContent.content) return '';

  let text = '';
  for (const node of tiptapContent.content) {
    if (node.type === 'paragraph' && node.content) {
      for (const textNode of node.content) {
        if (textNode.type === 'text') {
          text += textNode.text + '\n';
        }
      }
    } else if (node.type === 'heading' && node.content) {
      const level = node.attrs?.level || 1;
      const prefix = '#'.repeat(level) + ' ';
      for (const textNode of node.content) {
        if (textNode.type === 'text') {
          text += prefix + textNode.text + '\n';
        }
      }
    }
  }
  return text;
};

export function SimpleEditor({ content, onChange, editable = true }: SimpleEditorProps) {
  const [textContent, setTextContent] = useState(() => {
    if (content && content.content) {
      // 从TipTap JSON提取文本
      return extractTextFromTipTap(content);
    }
    return '开始编写您的文章...';
  });

  const convertToTipTap = (text: string): any => {
    const lines = text.split('\n').filter(line => line.trim());
    const content = [];

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // 检测标题
      if (trimmedLine.startsWith('#')) {
        const level = trimmedLine.match(/^#+/)?.[0].length || 1;
        const text = trimmedLine.replace(/^#+\s*/, '');
        content.push({
          type: 'heading',
          attrs: { level: Math.min(level, 6) },
          content: [{ type: 'text', text }],
        });
      } else {
        // 普通段落
        content.push({
          type: 'paragraph',
          content: [{ type: 'text', text: trimmedLine }],
        });
      }
    }

    return {
      type: 'doc',
      content: content.length > 0 ? content : [
        {
          type: 'paragraph',
          content: [{ type: 'text', text: '开始编写您的文章...' }],
        },
      ],
    };
  };

  const handleChange = (value: string) => {
    setTextContent(value);
    const tiptapContent = convertToTipTap(value);
    onChange?.(tiptapContent);
  };

  const insertComponent = (componentType: string) => {
    let insertText = '';
    
    switch (componentType) {
      case 'image':
        const imageUrl = window.prompt('请输入图片URL:');
        if (imageUrl) {
          insertText = `\n![图片描述](${imageUrl})\n`;
        }
        break;
      case 'highlight':
        const highlightText = window.prompt('请输入高亮内容:');
        if (highlightText) {
          insertText = `\n> 💡 ${highlightText}\n`;
        }
        break;
      case 'code':
        insertText = '\n```javascript\nconsole.log("Hello World");\n```\n';
        break;
      case 'section':
        insertText = '\n## 新章节\n\n在这里添加章节内容...\n';
        break;
      case 'divider':
        insertText = '\n---\n';
        break;
    }
    
    if (insertText) {
      const newContent = textContent + insertText;
      handleChange(newContent);
    }
  };

  return (
    <div className="simple-editor">
      {/* 工具栏 */}
      <Card className="mb-4 bg-slate-800/50 border-slate-700">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">编辑工具</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex gap-2 flex-wrap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertComponent('image')}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              📷 图片
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertComponent('highlight')}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              💡 高亮
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertComponent('code')}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              💻 代码
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertComponent('section')}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              📝 章节
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertComponent('divider')}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              ➖ 分割线
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 编辑器 */}
      <Textarea
        value={textContent}
        onChange={(e) => handleChange(e.target.value)}
        className="min-h-[400px] bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 font-mono"
        placeholder="开始编写您的文章...

支持Markdown语法：
# 一级标题
## 二级标题
![图片描述](图片URL)
> 引用内容
```代码块```
---分割线---"
        disabled={!editable}
      />
      
      <div className="mt-2 text-xs text-slate-500">
        💡 提示：支持Markdown语法，使用工具栏快速插入组件
      </div>
    </div>
  );
}
