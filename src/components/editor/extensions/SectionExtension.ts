import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';

// 定义Section节点的属性接口
export interface SectionAttributes {
  className?: string;
  title?: string;
}

// Section扩展
export const SectionExtension = Node.create({
  name: 'section',
  
  group: 'block',
  
  content: 'block+',
  
  defining: true,
  
  addAttributes() {
    return {
      className: {
        default: '',
        parseHTML: element => element.getAttribute('class') || '',
        renderHTML: attributes => {
          if (!attributes.className) {
            return {};
          }
          return { class: attributes.className };
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {};
          }
          return { 'data-title': attributes.title };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'section[data-component="section"]',
      },
      {
        tag: 'div[data-component="section"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'section',
      mergeAttributes(HTMLAttributes, { 'data-component': 'section' }),
      0, // 内容插槽
    ];
  },
  
  addCommands() {
    return {
      setSection: (attributes: SectionAttributes) => ({ commands }) => {
        return commands.wrapIn(this.name, attributes);
      },
      toggleSection: (attributes: SectionAttributes) => ({ commands }) => {
        return commands.toggleWrap(this.name, attributes);
      },
      updateSection: (attributes: Partial<SectionAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(SectionNodeView);
  },
});

// React节点视图组件
import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent, NodeViewProps } from '@tiptap/react';
import { Section } from '@/app/posts/components/Section';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Check, X, Layout } from 'lucide-react';

export function SectionNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    className: node.attrs.className || '',
    title: node.attrs.title || '',
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      className: node.attrs.className || '',
      title: node.attrs.title || '',
    });
    setIsEditing(false);
  };

  return (
    <NodeViewWrapper className={`section-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑章节
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="section-title" className="text-slate-300">章节标题（可选）</Label>
              <Input
                id="section-title"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入章节标题"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="section-class" className="text-slate-300">CSS类名（可选）</Label>
              <Input
                id="section-class"
                value={editData.className}
                onChange={(e) => setEditData(prev => ({ ...prev, className: e.target.value }))}
                placeholder="输入CSS类名"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <Section className={node.attrs.className}>
            {node.attrs.title && (
              <h3 className="section-title text-lg font-semibold mb-4 text-cyan-400">
                {node.attrs.title}
              </h3>
            )}
            <NodeViewContent className="section-content" />
          </Section>
        </div>
      )}
    </NodeViewWrapper>
  );
}

// Divider扩展
export const DividerExtension = Node.create({
  name: 'divider',
  
  group: 'block',
  
  atom: true,
  
  addAttributes() {
    return {
      style: {
        default: {},
        parseHTML: element => {
          const style = element.getAttribute('style');
          if (style) {
            // 简单解析style属性
            const styleObj: any = {};
            style.split(';').forEach(rule => {
              const [property, value] = rule.split(':').map(s => s.trim());
              if (property && value) {
                styleObj[property] = value;
              }
            });
            return styleObj;
          }
          return {};
        },
        renderHTML: attributes => {
          if (!attributes.style || Object.keys(attributes.style).length === 0) {
            return {};
          }
          const styleString = Object.entries(attributes.style)
            .map(([key, value]) => `${key}: ${value}`)
            .join('; ');
          return { style: styleString };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'hr[data-component="divider"]',
      },
      {
        tag: 'hr',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'hr',
      mergeAttributes(HTMLAttributes, { 'data-component': 'divider' }),
    ];
  },
  
  addCommands() {
    return {
      setDivider: (attributes?: { style?: any }) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
        });
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(DividerNodeView);
  },
});

// Divider React节点视图组件
import { Divider } from '@/app/posts/components/Section';
import { Minus } from 'lucide-react';

export function DividerNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    style: node.attrs.style || {},
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      style: node.attrs.style || {},
    });
    setIsEditing(false);
  };

  return (
    <NodeViewWrapper className={`divider-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑分割线
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-slate-300">分割线样式</Label>
              <p className="text-slate-400 text-sm">
                当前使用默认的赛博朋克风格分割线
              </p>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="w-4 h-4 mr-1" />
                确定
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-1/2 right-2 transform -translate-y-1/2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <Divider style={node.attrs.style} />
        </div>
      )}
    </NodeViewWrapper>
  );
}
