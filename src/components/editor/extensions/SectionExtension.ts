import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
// 导入React节点视图组件
import { SectionNodeView } from './SectionNodeView';
import { DividerNodeView } from './DividerNodeView';

// 定义Section节点的属性接口
export interface SectionAttributes {
  className?: string;
  title?: string;
}

// Section扩展
export const SectionExtension = Node.create({
  name: 'section',
  
  group: 'block',
  
  content: 'block+',
  
  defining: true,
  
  addAttributes() {
    return {
      className: {
        default: '',
        parseHTML: element => element.getAttribute('class') || '',
        renderHTML: attributes => {
          if (!attributes.className) {
            return {};
          }
          return { class: attributes.className };
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {};
          }
          return { 'data-title': attributes.title };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'section[data-component="section"]',
      },
      {
        tag: 'div[data-component="section"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'section',
      mergeAttributes(HTMLAttributes, { 'data-component': 'section' }),
      0, // 内容插槽
    ];
  },
  
  addCommands() {
    return {
      setSection: (attributes: SectionAttributes) => ({ commands }) => {
        return commands.wrapIn(this.name, attributes);
      },
      toggleSection: (attributes: SectionAttributes) => ({ commands }) => {
        return commands.toggleWrap(this.name, attributes);
      },
      updateSection: (attributes: Partial<SectionAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(SectionNodeView);
  },
});

// Divider扩展
export const DividerExtension = Node.create({
  name: 'divider',
  
  group: 'block',
  
  atom: true,
  
  addAttributes() {
    return {
      style: {
        default: {},
        parseHTML: element => {
          const style = element.getAttribute('style');
          if (style) {
            // 简单解析style属性
            const styleObj: any = {};
            style.split(';').forEach(rule => {
              const [property, value] = rule.split(':').map(s => s.trim());
              if (property && value) {
                styleObj[property] = value;
              }
            });
            return styleObj;
          }
          return {};
        },
        renderHTML: attributes => {
          if (!attributes.style || Object.keys(attributes.style).length === 0) {
            return {};
          }
          const styleString = Object.entries(attributes.style)
            .map(([key, value]) => `${key}: ${value}`)
            .join('; ');
          return { style: styleString };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'hr[data-component="divider"]',
      },
      {
        tag: 'hr',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'hr',
      mergeAttributes(HTMLAttributes, { 'data-component': 'divider' }),
    ];
  },
  
  addCommands() {
    return {
      setDivider: (attributes?: { style?: any }) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
        });
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(DividerNodeView);
  },
});
