'use client';

import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react';
import { Divider } from '@/app/posts/components/Section';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Check, X } from 'lucide-react';

export function DividerNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    style: node.attrs.style || {},
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      style: node.attrs.style || {},
    });
    setIsEditing(false);
  };

  return (
    <NodeViewWrapper className={`divider-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑分割线
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-slate-300">分割线样式</Label>
              <p className="text-slate-400 text-sm">
                当前使用默认的赛博朋克风格分割线
              </p>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700 cursor-pointer"
              >
                <Check className="w-4 h-4 mr-1" />
                确定
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 cursor-pointer hover:bg-slate-700"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-1/2 right-2 transform -translate-y-1/2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700 cursor-pointer"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <Divider style={node.attrs.style} />
        </div>
      )}
    </NodeViewWrapper>
  );
}
