'use client';

import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent, NodeViewProps } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Edit, Check, X, Copy } from 'lucide-react';

// 常用编程语言列表
const languages = [
  { value: 'text', label: '纯文本' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'c', label: 'C' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'swift', label: 'Swift' },
  { value: 'kotlin', label: 'Kotlin' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'scss', label: 'SCSS' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'bash', label: 'Bash' },
  { value: 'sql', label: 'SQL' },
];

export function CodeBlockNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    language: node.attrs.language || 'text',
    title: node.attrs.title || '',
    showLineNumbers: node.attrs.showLineNumbers || false,
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      language: node.attrs.language || 'text',
      title: node.attrs.title || '',
      showLineNumbers: node.attrs.showLineNumbers || false,
    });
    setIsEditing(false);
  };

  const handleCopyCode = () => {
    const codeContent = node.textContent;
    navigator.clipboard.writeText(codeContent);
    // 这里可以添加复制成功的提示
  };

  return (
    <NodeViewWrapper className={`code-block-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑代码块
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="code-language" className="text-slate-300">编程语言</Label>
              <Select
                value={editData.language}
                onValueChange={(value) => setEditData(prev => ({ ...prev, language: value }))}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white cursor-pointer">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600 max-h-60">
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value} className="text-white cursor-pointer hover:bg-slate-600">
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="code-title" className="text-slate-300">标题（可选）</Label>
              <Input
                id="code-title"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入代码块标题"
                className="bg-slate-700 border-slate-600 text-white focus:border-cyan-400 focus:ring-cyan-400"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-line-numbers"
                checked={editData.showLineNumbers}
                onCheckedChange={(checked) => 
                  setEditData(prev => ({ ...prev, showLineNumbers: !!checked }))
                }
                className="cursor-pointer"
              />
              <Label htmlFor="show-line-numbers" className="text-slate-300 cursor-pointer">
                显示行号
              </Label>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700 cursor-pointer"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 cursor-pointer hover:bg-slate-700"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
              <Button
                onClick={handleCopyCode}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700 cursor-pointer"
              >
                <Copy className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700 cursor-pointer"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <div className="code-block-wrapper bg-slate-900 rounded-lg border border-slate-700">
            {/* 代码块头部 */}
            <div className="code-block-header flex items-center justify-between px-4 py-2 bg-slate-800 rounded-t-lg border-b border-slate-700">
              {node.attrs.title && (
                <div className="code-block-title text-slate-300 font-medium">
                  {node.attrs.title}
                </div>
              )}
              <div className="code-block-language text-xs text-slate-400 bg-slate-700 px-2 py-1 rounded">
                {languages.find(lang => lang.value === node.attrs.language)?.label || node.attrs.language}
              </div>
            </div>
            
            {/* 代码内容 */}
            <pre className="code-block-content p-4 overflow-x-auto">
              <code className={`language-${node.attrs.language} text-slate-200`}>
                <NodeViewContent />
              </code>
            </pre>
          </div>
        </div>
      )}
    </NodeViewWrapper>
  );
}
