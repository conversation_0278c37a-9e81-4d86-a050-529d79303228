'use client';

import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent, NodeViewProps } from '@tiptap/react';
import { Section } from '@/app/posts/components/Section';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Check, X } from 'lucide-react';

export function SectionNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    className: node.attrs.className || '',
    title: node.attrs.title || '',
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      className: node.attrs.className || '',
      title: node.attrs.title || '',
    });
    setIsEditing(false);
  };

  return (
    <NodeViewWrapper className={`section-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑章节
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="section-title" className="text-slate-300">章节标题（可选）</Label>
              <Input
                id="section-title"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入章节标题"
                className="bg-slate-700 border-slate-600 text-white focus:border-cyan-400 focus:ring-cyan-400"
              />
            </div>
            
            <div>
              <Label htmlFor="section-class" className="text-slate-300">CSS类名（可选）</Label>
              <Input
                id="section-class"
                value={editData.className}
                onChange={(e) => setEditData(prev => ({ ...prev, className: e.target.value }))}
                placeholder="输入CSS类名"
                className="bg-slate-700 border-slate-600 text-white focus:border-cyan-400 focus:ring-cyan-400"
              />
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700 cursor-pointer"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 cursor-pointer hover:bg-slate-700"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700 cursor-pointer"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <Section className={node.attrs.className}>
            {node.attrs.title && (
              <h3 className="section-title text-lg font-semibold mb-4 text-cyan-400">
                {node.attrs.title}
              </h3>
            )}
            <NodeViewContent className="section-content" />
          </Section>
        </div>
      )}
    </NodeViewWrapper>
  );
}
