import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
// 导入React节点视图组件
import { HighlightBoxNodeView } from './HighlightBoxNodeView';

// 定义HighlightBox节点的属性接口
export interface HighlightBoxAttributes {
  type: 'info' | 'warning' | 'success' | 'error';
  title?: string;
}

// HighlightBox扩展
export const HighlightBoxExtension = Node.create({
  name: 'highlightBox',
  
  group: 'block',
  
  content: 'block+',
  
  defining: true,
  
  addAttributes() {
    return {
      type: {
        default: 'info',
        parseHTML: element => element.getAttribute('data-type') || 'info',
        renderHTML: attributes => {
          return { 'data-type': attributes.type };
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {};
          }
          return { 'data-title': attributes.title };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-component="highlight-box"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes, node }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-component': 'highlight-box' }),
      0, // 内容插槽
    ];
  },
  
  addCommands() {
    return {
      setHighlightBox: (attributes: HighlightBoxAttributes) => ({ commands }) => {
        return commands.wrapIn(this.name, attributes);
      },
      toggleHighlightBox: (attributes: HighlightBoxAttributes) => ({ commands }) => {
        return commands.toggleWrap(this.name, attributes);
      },
      updateHighlightBox: (attributes: Partial<HighlightBoxAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(HighlightBoxNodeView);
  },
});
