import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';

// 定义HighlightBox节点的属性接口
export interface HighlightBoxAttributes {
  type: 'info' | 'warning' | 'success' | 'error';
  title?: string;
}

// HighlightBox扩展
export const HighlightBoxExtension = Node.create({
  name: 'highlightBox',
  
  group: 'block',
  
  content: 'block+',
  
  defining: true,
  
  addAttributes() {
    return {
      type: {
        default: 'info',
        parseHTML: element => element.getAttribute('data-type') || 'info',
        renderHTML: attributes => {
          return { 'data-type': attributes.type };
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {};
          }
          return { 'data-title': attributes.title };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-component="highlight-box"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes, node }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-component': 'highlight-box' }),
      0, // 内容插槽
    ];
  },
  
  addCommands() {
    return {
      setHighlightBox: (attributes: HighlightBoxAttributes) => ({ commands }) => {
        return commands.wrapIn(this.name, attributes);
      },
      toggleHighlightBox: (attributes: HighlightBoxAttributes) => ({ commands }) => {
        return commands.toggleWrap(this.name, attributes);
      },
      updateHighlightBox: (attributes: Partial<HighlightBoxAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(HighlightBoxNodeView);
  },
});

// React节点视图组件
import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent, NodeViewProps } from '@tiptap/react';
import { HighlightBox } from '@/app/posts/components/HighlightBox';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Check, X, AlertCircle, Info, CheckCircle, AlertTriangle } from 'lucide-react';

const typeIcons = {
  info: Info,
  warning: AlertTriangle,
  success: CheckCircle,
  error: AlertCircle,
};

const typeLabels = {
  info: '信息',
  warning: '警告',
  success: '成功',
  error: '错误',
};

export function HighlightBoxNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    type: node.attrs.type || 'info',
    title: node.attrs.title || '',
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      type: node.attrs.type || 'info',
      title: node.attrs.title || '',
    });
    setIsEditing(false);
  };

  const IconComponent = typeIcons[node.attrs.type as keyof typeof typeIcons] || Info;

  return (
    <NodeViewWrapper className={`highlight-box-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑高亮框
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="highlight-type" className="text-slate-300">类型</Label>
              <Select
                value={editData.type}
                onValueChange={(value) => setEditData(prev => ({ ...prev, type: value as any }))}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  {Object.entries(typeLabels).map(([value, label]) => {
                    const Icon = typeIcons[value as keyof typeof typeIcons];
                    return (
                      <SelectItem key={value} value={value} className="text-white">
                        <div className="flex items-center gap-2">
                          <Icon className="w-4 h-4" />
                          {label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="highlight-title" className="text-slate-300">标题（可选）</Label>
              <Input
                id="highlight-title"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入高亮框标题"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <HighlightBox
            type={node.attrs.type}
            title={node.attrs.title}
          >
            <NodeViewContent className="highlight-box-content" />
          </HighlightBox>
        </div>
      )}
    </NodeViewWrapper>
  );
}
