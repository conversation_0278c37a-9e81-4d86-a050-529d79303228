import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';

// 定义ImageContainer节点的属性接口
export interface ImageContainerAttributes {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  caption?: string;
}

// ImageContainer扩展
export const ImageContainerExtension = Node.create({
  name: 'imageContainer',
  
  group: 'block',
  
  atom: true,
  
  draggable: true,
  
  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: element => element.getAttribute('src'),
        renderHTML: attributes => {
          if (!attributes.src) {
            return {};
          }
          return { src: attributes.src };
        },
      },
      alt: {
        default: '',
        parseHTML: element => element.getAttribute('alt'),
        renderHTML: attributes => {
          if (!attributes.alt) {
            return {};
          }
          return { alt: attributes.alt };
        },
      },
      width: {
        default: '100%',
        parseHTML: element => element.getAttribute('data-width'),
        renderHTML: attributes => {
          if (!attributes.width) {
            return {};
          }
          return { 'data-width': attributes.width };
        },
      },
      height: {
        default: 'auto',
        parseHTML: element => element.getAttribute('data-height'),
        renderHTML: attributes => {
          if (!attributes.height) {
            return {};
          }
          return { 'data-height': attributes.height };
        },
      },
      caption: {
        default: null,
        parseHTML: element => element.getAttribute('data-caption'),
        renderHTML: attributes => {
          if (!attributes.caption) {
            return {};
          }
          return { 'data-caption': attributes.caption };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="image-container"]',
      },
      {
        tag: 'img',
        getAttrs: element => {
          const img = element as HTMLImageElement;
          return {
            src: img.src,
            alt: img.alt,
            width: img.getAttribute('data-width') || '100%',
            height: img.getAttribute('data-height') || 'auto',
            caption: img.getAttribute('data-caption'),
          };
        },
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-type': 'image-container' }),
      [
        'img',
        {
          src: HTMLAttributes.src,
          alt: HTMLAttributes.alt,
          'data-width': HTMLAttributes.width,
          'data-height': HTMLAttributes.height,
          'data-caption': HTMLAttributes.caption,
        },
      ],
    ];
  },
  
  addCommands() {
    return {
      setImageContainer: (attributes: ImageContainerAttributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
        });
      },
      updateImageContainer: (attributes: Partial<ImageContainerAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(ImageContainerNodeView);
  },
});

// React节点视图组件
import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react';
import { ImageContainer } from '@/app/posts/components/ImageContainer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Check, X, Upload } from 'lucide-react';

export function ImageContainerNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    src: node.attrs.src || '',
    alt: node.attrs.alt || '',
    width: node.attrs.width || '100%',
    height: node.attrs.height || 'auto',
    caption: node.attrs.caption || '',
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      src: node.attrs.src || '',
      alt: node.attrs.alt || '',
      width: node.attrs.width || '100%',
      height: node.attrs.height || 'auto',
      caption: node.attrs.caption || '',
    });
    setIsEditing(false);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 这里可以实现图片上传逻辑
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setEditData(prev => ({ ...prev, src: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <NodeViewWrapper className={`image-container-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑图片
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="image-src" className="text-slate-300">图片URL</Label>
              <div className="flex gap-2">
                <Input
                  id="image-src"
                  value={editData.src}
                  onChange={(e) => setEditData(prev => ({ ...prev, src: e.target.value }))}
                  placeholder="输入图片URL或上传图片"
                  className="bg-slate-700 border-slate-600 text-white"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300"
                  onClick={() => document.getElementById('image-upload')?.click()}
                >
                  <Upload className="w-4 h-4" />
                </Button>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="image-alt" className="text-slate-300">图片描述</Label>
              <Input
                id="image-alt"
                value={editData.alt}
                onChange={(e) => setEditData(prev => ({ ...prev, alt: e.target.value }))}
                placeholder="图片描述文字"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="image-caption" className="text-slate-300">图片说明</Label>
              <Input
                id="image-caption"
                value={editData.caption}
                onChange={(e) => setEditData(prev => ({ ...prev, caption: e.target.value }))}
                placeholder="图片说明文字（可选）"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div className="flex gap-2">
              <div className="flex-1">
                <Label htmlFor="image-width" className="text-slate-300">宽度</Label>
                <Input
                  id="image-width"
                  value={editData.width}
                  onChange={(e) => setEditData(prev => ({ ...prev, width: e.target.value }))}
                  placeholder="100%"
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              <div className="flex-1">
                <Label htmlFor="image-height" className="text-slate-300">高度</Label>
                <Input
                  id="image-height"
                  value={editData.height}
                  onChange={(e) => setEditData(prev => ({ ...prev, height: e.target.value }))}
                  placeholder="auto"
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          <ImageContainer
            src={node.attrs.src}
            alt={node.attrs.alt}
            width={node.attrs.width}
            height={node.attrs.height}
            caption={node.attrs.caption}
          />
          {selected && (
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      )}
    </NodeViewWrapper>
  );
}
