import { Node, mergeAttributes, textblockTypeInputRule } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';

// 定义CodeBlock节点的属性接口
export interface CodeBlockAttributes {
  language: string;
  title?: string;
  showLineNumbers?: boolean;
}

// CodeBlock扩展
export const CodeBlockExtension = Node.create({
  name: 'codeBlock',
  
  group: 'block',
  
  content: 'text*',
  
  marks: '',
  
  code: true,
  
  defining: true,
  
  addAttributes() {
    return {
      language: {
        default: 'text',
        parseHTML: element => {
          const classNames = element.className;
          const match = classNames.match(/language-(\w+)/);
          return match ? match[1] : 'text';
        },
        renderHTML: attributes => {
          if (!attributes.language) {
            return {};
          }
          return { class: `language-${attributes.language}` };
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {};
          }
          return { 'data-title': attributes.title };
        },
      },
      showLineNumbers: {
        default: false,
        parseHTML: element => element.getAttribute('data-line-numbers') === 'true',
        renderHTML: attributes => {
          if (!attributes.showLineNumbers) {
            return {};
          }
          return { 'data-line-numbers': 'true' };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'pre',
        preserveWhitespace: 'full',
      },
      {
        tag: 'div[data-component="code-block"]',
        preserveWhitespace: 'full',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes, node }) {
    return [
      'pre',
      mergeAttributes(HTMLAttributes, { 'data-component': 'code-block' }),
      ['code', {}, 0],
    ];
  },
  
  addCommands() {
    return {
      setCodeBlock: (attributes: CodeBlockAttributes) => ({ commands }) => {
        return commands.setNode(this.name, attributes);
      },
      toggleCodeBlock: (attributes: CodeBlockAttributes) => ({ commands }) => {
        return commands.toggleNode(this.name, 'paragraph', attributes);
      },
      updateCodeBlock: (attributes: Partial<CodeBlockAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addInputRules() {
    return [
      textblockTypeInputRule({
        find: /^```([a-z]+)?[\s\n]$/,
        type: this.type,
        getAttributes: match => ({
          language: match[1] || 'text',
        }),
      }),
    ];
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(CodeBlockNodeView);
  },
});

// React节点视图组件
import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent, NodeViewProps } from '@tiptap/react';
import { CodeBlock } from '@/app/posts/components/CodeBlock';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Edit, Check, X, Code, Copy } from 'lucide-react';

// 常用编程语言列表
const languages = [
  { value: 'text', label: '纯文本' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'c', label: 'C' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'swift', label: 'Swift' },
  { value: 'kotlin', label: 'Kotlin' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'scss', label: 'SCSS' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'bash', label: 'Bash' },
  { value: 'sql', label: 'SQL' },
];

export function CodeBlockNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    language: node.attrs.language || 'text',
    title: node.attrs.title || '',
    showLineNumbers: node.attrs.showLineNumbers || false,
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      language: node.attrs.language || 'text',
      title: node.attrs.title || '',
      showLineNumbers: node.attrs.showLineNumbers || false,
    });
    setIsEditing(false);
  };

  const handleCopyCode = () => {
    const codeContent = node.textContent;
    navigator.clipboard.writeText(codeContent);
    // 这里可以添加复制成功的提示
  };

  return (
    <NodeViewWrapper className={`code-block-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600 mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑代码块
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="code-language" className="text-slate-300">编程语言</Label>
              <Select
                value={editData.language}
                onValueChange={(value) => setEditData(prev => ({ ...prev, language: value }))}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600 max-h-60">
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value} className="text-white">
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="code-title" className="text-slate-300">标题（可选）</Label>
              <Input
                id="code-title"
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入代码块标题"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-line-numbers"
                checked={editData.showLineNumbers}
                onCheckedChange={(checked) => 
                  setEditData(prev => ({ ...prev, showLineNumbers: !!checked }))
                }
              />
              <Label htmlFor="show-line-numbers" className="text-slate-300">
                显示行号
              </Label>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          {selected && (
            <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
              <Button
                onClick={handleCopyCode}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700"
              >
                <Copy className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <div className="code-block-wrapper">
            {/* 使用自定义的代码块渲染 */}
            <div className="code-block-header">
              {node.attrs.title && (
                <div className="code-block-title">
                  {node.attrs.title}
                </div>
              )}
              <div className="code-block-language">
                {languages.find(lang => lang.value === node.attrs.language)?.label || node.attrs.language}
              </div>
            </div>
            
            <pre className="code-block-content">
              <code className={`language-${node.attrs.language}`}>
                <NodeViewContent />
              </code>
            </pre>
          </div>
        </div>
      )}
    </NodeViewWrapper>
  );
}
