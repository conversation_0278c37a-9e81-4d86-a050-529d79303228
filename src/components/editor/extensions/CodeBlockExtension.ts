import { Node, mergeAttributes, textblockTypeInputRule } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
// 导入React节点视图组件
import { CodeBlockNodeView } from './CodeBlockNodeView';

// 定义CodeBlock节点的属性接口
export interface CodeBlockAttributes {
  language: string;
  title?: string;
  showLineNumbers?: boolean;
}

// CodeBlock扩展
export const CodeBlockExtension = Node.create({
  name: 'codeBlock',
  
  group: 'block',
  
  content: 'text*',
  
  marks: '',
  
  code: true,
  
  defining: true,
  
  addAttributes() {
    return {
      language: {
        default: 'text',
        parseHTML: element => {
          const classNames = element.className;
          const match = classNames.match(/language-(\w+)/);
          return match ? match[1] : 'text';
        },
        renderHTML: attributes => {
          if (!attributes.language) {
            return {};
          }
          return { class: `language-${attributes.language}` };
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {};
          }
          return { 'data-title': attributes.title };
        },
      },
      showLineNumbers: {
        default: false,
        parseHTML: element => element.getAttribute('data-line-numbers') === 'true',
        renderHTML: attributes => {
          if (!attributes.showLineNumbers) {
            return {};
          }
          return { 'data-line-numbers': 'true' };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'pre',
        preserveWhitespace: 'full',
      },
      {
        tag: 'div[data-component="code-block"]',
        preserveWhitespace: 'full',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes, node }) {
    return [
      'pre',
      mergeAttributes(HTMLAttributes, { 'data-component': 'code-block' }),
      ['code', {}, 0],
    ];
  },
  
  addCommands() {
    return {
      setCodeBlock: (attributes: CodeBlockAttributes) => ({ commands }) => {
        return commands.setNode(this.name, attributes);
      },
      toggleCodeBlock: (attributes: CodeBlockAttributes) => ({ commands }) => {
        return commands.toggleNode(this.name, 'paragraph', attributes);
      },
      updateCodeBlock: (attributes: Partial<CodeBlockAttributes>) => ({ commands }) => {
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },
  
  addInputRules() {
    return [
      textblockTypeInputRule({
        find: /^```([a-z]+)?[\s\n]$/,
        type: this.type,
        getAttributes: match => ({
          language: match[1] || 'text',
        }),
      }),
    ];
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(CodeBlockNodeView);
  },
});
