'use client';

import { useState } from 'react';
import { PostData } from '@/lib/posts';

interface PostEditorProps {
  post: PostData; // post.slug is dateSlug, post.articleFileName is the .md file name
}

export default function PostEditor({ post }: PostEditorProps) {
  const [title, setTitle] = useState(post.title);
  const [content, setContent] = useState(post.content);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState('split'); // 'split', 'edit', 'preview'

  const handleSave = async () => {
    if (!post.articleFileName) {
      alert('无法确定文章文件名，无法保存。');
      return;
    }
    setIsSaving(true);
    try {
      // post.slug is the dateFolder (e.g., 2025-05-25)
      // We need to pass the articleFileName to identify the correct .md file
      const response = await fetch(`/api/posts/${post.slug}`, { // API endpoint uses dateSlug
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleFileName: post.articleFileName, // Send the specific markdown file name
          title: title.trim(),
          content: content.trim(),
          // Potentially send other metadata if it can be edited here
          // metadata: { date: post.date, ... } // Example
        }),
      });

      if (response.ok) {
        setIsEditing(false);
        // TODO: Add success notification (e.g., toast)
        // Refresh data or update state if necessary, though Next.js router cache might handle some of this
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || '保存失败');
      }
    } catch (error) {
      console.error('Error saving post:', error);
      alert(`保存失败: ${(error as Error).message}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Basic Markdown to HTML renderer (can be replaced with a more robust library)
  const renderMarkdown = (text: string) => {
    // Ensure p tags are not nested, and lists are handled correctly.
    let html = text
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mb-2 text-white">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mb-3 text-white">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 text-white">$1</h1>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-bold text-white">$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em class="italic text-slate-300">$1</em>')
      .replace(/^\s*-\s+(.*$)/gim, '<li class="mb-1 ml-4 list-disc text-slate-300">$1</li>');

    // Wrap paragraphs correctly, handling existing lists
    html = html.split('\n\n').map(paragraph => {
      if (paragraph.startsWith('<li')) {
        // This is part of a list, wrap with <ul> if not already done by a previous block
        // This simple logic might need improvement for complex list structures
        return `<ul class="mb-4">${paragraph.replace(/<\/li>\s*<li/g, '</li><li')}</ul>`;
      }
      if (paragraph.trim() === '') return '';
      if (paragraph.startsWith('<')) return paragraph; // Already HTML (h1,h2,h3,ul)
      return `<p class="mb-4 text-slate-300 leading-relaxed">${paragraph}</p>`;
    }).join('');

    // Clean up multiple <ul> tags if lists are consecutive
    html = html.replace(/<\/ul>\s*<ul class="mb-4">/g, '\n');

    return html;
  };

  return (
    <div className="space-y-6">
      {/* 工具栏 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div className="flex gap-2">
          <button
            onClick={() => setPreviewMode('edit')}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
              previewMode === 'edit'
                ? 'bg-cyan-500 text-white'
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            📝 编辑
          </button>
          <button
            onClick={() => setPreviewMode('split')}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
              previewMode === 'split'
                ? 'bg-cyan-500 text-white'
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            📱 分屏
          </button>
          <button
            onClick={() => setPreviewMode('preview')}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
              previewMode === 'preview'
                ? 'bg-cyan-500 text-white'
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            👁️ 预览
          </button>
        </div>

        <div className="flex gap-3">
          {isEditing ? (
            <>
              <button
                onClick={() => {
                  setTitle(post.title);
                  setContent(post.content);
                  setIsEditing(false);
                }}
                className="px-4 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-800/50 transition-all duration-200"
              >
                取消
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium disabled:opacity-50"
              >
                {isSaving ? '保存中...' : '💾 保存'}
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 font-medium"
            >
              ✏️ 编辑
            </button>
          )}
        </div>
      </div>

      {/* 标题编辑 */}
      {isEditing && (
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            标题
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
          />
        </div>
      )}

      {/* 内容区域 */}
      <div className="grid gap-6" style={{
        gridTemplateColumns: previewMode === 'split' ? '1fr 1fr' : '1fr'
      }}>
        {/* 编辑器 */}
        {(previewMode === 'edit' || previewMode === 'split') && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">
              内容 (Markdown)
            </label>
            <textarea
              value={content}
              onChange={(e) => {
                setContent(e.target.value);
                if (!isEditing) setIsEditing(true);
              }}
              rows={30}
              className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent resize-none"
              placeholder="开始编写您的文章内容..."
            />
          </div>
        )}

        {/* 预览 */}
        {(previewMode === 'preview' || previewMode === 'split') && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">
              预览
            </label>
            <div 
              className="min-h-[750px] px-4 py-3 bg-slate-800/30 border border-slate-600/30 rounded-lg overflow-auto prose prose-invert prose-sm max-w-none"
              dangerouslySetInnerHTML={{ 
                __html: renderMarkdown(content) 
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
} 