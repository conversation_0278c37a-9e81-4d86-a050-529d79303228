"use client"

import * as React from "react"
import { <PERSON>, Sun, Monitor } from "lucide-react"
import { useTheme } from "next-themes"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function ThemeToggle() {
  const { setTheme, theme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="bg-background/80 backdrop-blur-sm border-cyan-500/30 hover:border-pink-500/50 transition-colors">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-cyan-400" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-pink-400" />
          <span className="sr-only">切换主题</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-background/95 backdrop-blur-sm border-cyan-500/30">
        <DropdownMenuItem 
          onClick={() => setTheme("light")}
          className="hover:bg-cyan-500/10 focus:bg-cyan-500/10 cursor-pointer"
        >
          <Sun className="mr-2 h-4 w-4 text-cyan-400" />
          <span>明亮模式</span>
          {theme === "light" && <span className="ml-auto text-cyan-400">✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setTheme("dark")}
          className="hover:bg-pink-500/10 focus:bg-pink-500/10 cursor-pointer"
        >
          <Moon className="mr-2 h-4 w-4 text-pink-400" />
          <span>暗黑模式</span>
          {theme === "dark" && <span className="ml-auto text-pink-400">✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setTheme("system")}
          className="hover:bg-purple-500/10 focus:bg-purple-500/10 cursor-pointer"
        >
          <Monitor className="mr-2 h-4 w-4 text-purple-400" />
          <span>跟随系统</span>
          {theme === "system" && <span className="ml-auto text-purple-400">✓</span>}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
