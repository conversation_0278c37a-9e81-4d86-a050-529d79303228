This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 项目结构

```
src/
  ├── app/                    # 应用页面目录
  │   ├── api/                # API路由
  │   ├── cover/              # 封面制作工具
  │   └── posts/              # 文章相关页面
  ├── components/             # 组件目录
  │   ├── layout/             # 布局相关组件
  │   │   ├── ArticleLayout.tsx  # 文章页面布局
  │   │   ├── Header.tsx      # 头部导航
  │   │   └── CoverTemplate.tsx # 封面模板
  │   ├── post/               # 文章相关组件
  │   │   ├── PostCard.tsx    # 文章卡片
  │   │   └── HtmlPostViewer.tsx # HTML文章查看器
  │   └── editor/             # 编辑器相关组件
  │       └── PostEditor.tsx  # 文章编辑器
  ├── lib/                    # 工具库
  └── styles/                 # 样式文件
      └── blogStyles.css      # 博客样式
```

## 文章布局组件

文章布局组件 `ArticleLayout` 提供了统一的文章展示模板，具有以下特性：

1. **响应式设计**：适应各种屏幕尺寸
2. **手机壳预览**：提供手机壳预览模式，最大高度为屏幕的80%，带滚动条
3. **品牌页脚**：展示品牌信息
4. **优化的复制功能**：一键复制富文本内容到微信公众号，自动处理图片路径和样式
5. **集成封面生成器**：内置封面图片生成工具，支持多种预设模板和自定义配置

使用方法：

```tsx
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';

// 创建文章元数据
const metadata: ArticleMetadata = {
  title: '文章标题',
  date: '2025-05-25',
  excerpt: '文章摘要（可选）'
};

// 使用ArticleLayout组件
export default function MyPage() {
  return (
    <ArticleLayout metadata={metadata}>
      <div className="article-content">
        {/* 文章内容 */}
      </div>
    </ArticleLayout>
  );
}
```

### 新增功能详解

#### 1. 优化的复制功能

新的复制功能不再是简单的HTML复制，而是：
- **富文本格式复制**：复制的内容包含完整的样式信息
- **图片路径自动处理**：将本地路径（如 `/posts/xxx`）转换为绝对路径
- **微信公众号兼容**：复制的内容可直接粘贴到微信公众号编辑器
- **内联样式**：所有样式都内联到HTML中，确保在微信编辑器中正确显示

#### 2. 弹窗式封面生成器

优化的弹窗式封面生成器，解决了预览比例和生成质量问题：
- **弹窗模式**：提供更大的预览空间，确保预览效果与生成效果一致
- **标准比例**：严格按照微信公众号封面尺寸（900:383）等比例显示
- **高质量生成**：使用 html2canvas 生成 2x 分辨率的高清图片
- **响应式设计**：在桌面和移动设备上都能完美使用
- **预设模板**：支持 Cursor、AI工具、编程技巧、科技评测等系列
- **实时预览**：修改配置后立即看到效果
- **一键复制**：生成的封面图片可直接复制到剪贴板

使用步骤：
1. 在文章页面点击"🎨 生成封面图片"按钮
2. 在弹窗中选择预设模板或自定义配置
3. 在大尺寸预览区域查看效果
4. 点击"📋 复制封面图片"按钮
5. 直接粘贴到微信公众号或其他平台

**优化亮点**：
- 解决了原有内联显示时预览过小的问题
- 确保预览与最终生成图片完全一致
- 支持响应式字体大小，在不同屏幕下都能正确显示
- 移动端友好的操作界面

## 图片资源管理

本项目使用以下目录结构来管理图片资源：

```
public/
  ├── assets/              # 通用资源目录
  │   ├── avatar/          # 公众号头像
  │   │   └── avatar.jpg
  │   └── series/          # 系列主题资源
  │       ├── cursor/      # Cursor系列相关资源
  │       ├── ai-tools/    # AI工具系列相关资源
  │       ├── programming/ # 编程技巧系列相关资源
  │       └── tech-review/ # 科技评测系列相关资源
  └── posts/               # 文章特定资源
      └── YYYY-MM-DD/      # 按日期组织
          └── img/         # 文章图片目录
```

### 封面制作工具

本项目包含一个微信公众号封面制作工具，访问 `/cover` 路径即可使用。封面工具特性：

1. 支持多种预设系列主题
2. 可自定义系列标识、主标题和副标题
3. 可更换背景图片
4. 支持复制到剪贴板和下载PNG图片

### 文章图片管理指南

1. **通用资源**：放置在 `/public/assets/` 目录下
   - 公众号头像：`/public/assets/avatar/avatar.jpg`
   - 系列主题图片：`/public/assets/series/{series-name}/{image-name}.png`

2. **文章特定图片**：放置在 `/public/posts/YYYY-MM-DD/img/` 目录下
   - 例如：`/public/posts/2025-05-25/img/image-1.png`

3. **在文章中引用图片**：
   ```md
   ![图片描述](/posts/2025-05-25/img/image.png)
   ```