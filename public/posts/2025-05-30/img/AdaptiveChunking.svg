<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 570.390625 174" style="max-width: 570.390625px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-19"><style>#mermaid-svg-19{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#mermaid-svg-19 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#mermaid-svg-19 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#mermaid-svg-19 .error-icon{fill:#552222;}#mermaid-svg-19 .error-text{fill:#552222;stroke:#552222;}#mermaid-svg-19 .edge-thickness-normal{stroke-width:1px;}#mermaid-svg-19 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-19 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-19 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-svg-19 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-19 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-19 .marker{fill:#333333;stroke:#333333;}#mermaid-svg-19 .marker.cross{stroke:#333333;}#mermaid-svg-19 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-19 p{margin:0;}#mermaid-svg-19 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-svg-19 .cluster-label text{fill:#333;}#mermaid-svg-19 .cluster-label span{color:#333;}#mermaid-svg-19 .cluster-label span p{background-color:transparent;}#mermaid-svg-19 .label text,#mermaid-svg-19 span{fill:#333;color:#333;}#mermaid-svg-19 .node rect,#mermaid-svg-19 .node circle,#mermaid-svg-19 .node ellipse,#mermaid-svg-19 .node polygon,#mermaid-svg-19 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-svg-19 .rough-node .label text,#mermaid-svg-19 .node .label text,#mermaid-svg-19 .image-shape .label,#mermaid-svg-19 .icon-shape .label{text-anchor:middle;}#mermaid-svg-19 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-svg-19 .rough-node .label,#mermaid-svg-19 .node .label,#mermaid-svg-19 .image-shape .label,#mermaid-svg-19 .icon-shape .label{text-align:center;}#mermaid-svg-19 .node.clickable{cursor:pointer;}#mermaid-svg-19 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-svg-19 .arrowheadPath{fill:#333333;}#mermaid-svg-19 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-svg-19 .flowchart-link{stroke:#333333;fill:none;}#mermaid-svg-19 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-19 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-svg-19 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-19 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-svg-19 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-svg-19 .cluster text{fill:#333;}#mermaid-svg-19 .cluster span{color:#333;}#mermaid-svg-19 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-19 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-svg-19 rect.text{fill:none;stroke-width:0;}#mermaid-svg-19 .icon-shape,#mermaid-svg-19 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-19 .icon-shape p,#mermaid-svg-19 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-svg-19 .icon-shape rect,#mermaid-svg-19 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-19 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-19_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-19_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-19_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-19_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-19_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-19_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-19_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M132,87L136.167,87C140.333,87,148.667,87,156.417,87.07C164.167,87.141,171.334,87.281,174.917,87.351L178.501,87.422"></path><path marker-end="url(#mermaid-svg-19_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M281.325,68.325L292.604,62.771C303.883,57.217,326.442,46.108,345.221,40.554C364,35,379,35,386.5,35L394,35"></path><path marker-end="url(#mermaid-svg-19_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_0" d="M281.325,106.675L292.604,112.063C303.883,117.45,326.442,128.225,347.253,133.612C368.065,139,387.13,139,396.663,139L406.195,139"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(349, 35)" class="edgeLabel"><g transform="translate(-24, -12)" class="label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>架构级</p></span></div></foreignObject></g></g><g transform="translate(349, 139)" class="edgeLabel"><g transform="translate(-24, -12)" class="label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>逻辑级</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(70, 87)" id="flowchart-A-0" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户查询</p></span></div></foreignObject></g></g><g transform="translate(241, 87)" id="flowchart-B-1" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询类型</p></span></div></foreignObject></g></g><g transform="translate(480.1953125, 35)" id="flowchart-C-3" class="node default"><rect height="54" width="164.390625" y="-27" x="-82.1953125" style="" class="basic label-container"></rect><g transform="translate(-52.1953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>聚合类/模块块</p></span></div></foreignObject></g></g><g transform="translate(480.1953125, 139)" id="flowchart-D-5" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>函数级精读</p></span></div></foreignObject></g></g></g></g></g></svg>