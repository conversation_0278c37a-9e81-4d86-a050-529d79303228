# 图片文件说明

本目录包含文章《揭秘Cursor的「硅基大脑」：代码库索引如何重塑AI编程？》所需的图片文件。

## 需要的图片文件

### 1. merkle-tree-flow.png
**Merkle树增量同步流程图**

Mermaid 代码：
```mermaid
flowchart TB
    A[客户端扫描项目] --> B[计算文件哈希]
    B --> C[构建Merkle树]
    C --> D{每10分钟比对根哈希}
    D -- 变更 --> E[上传差异文件]
    D -- 未变 --> F[保持同步]
```

### 2. adaptive-chunking.png
**自适应分块策略图**

Mermaid 代码：
```mermaid
graph LR
   A[用户查询] --> B{查询类型}
   B -->|架构级| C[聚合类/模块块]
   B -->|逻辑级| D[函数级精读]
```

## 生成图片的方法

### 方法 1：使用 Mermaid Live Editor
1. 访问 https://mermaid.live/
2. 复制上面的 Mermaid 代码
3. 点击 "Actions" -> "Download PNG"
4. 将文件重命名并保存到此目录

### 方法 2：使用 VS Code 插件
1. 安装 "Mermaid Markdown Syntax Highlighting" 插件
2. 创建 .md 文件，粘贴 Mermaid 代码
3. 右键选择导出为图片

### 方法 3：使用命令行工具
```bash
npm install -g @mermaid-js/mermaid-cli
mmdc -i input.mmd -o output.png
```
