# 组件库使用指南

## 概述

本指南详细介绍了微信公众号文章组件库的使用方法。组件库位于 `src/app/posts/components/`，提供了一系列专门为微信公众号优化的 React 组件。

## 组件导入

```typescript
// 导入所有组件
import { 
  ImageContainer, 
  HighlightBox, 
  CodeBlock, 
  Section, 
  Divider 
} from '../components';

// 或单独导入
import { HighlightBox } from '../components/HighlightBox';
```

## 组件详细说明

### 1. ImageContainer - 图片容器组件

#### 功能特性
- 自动响应式布局
- 支持图片说明文字
- 微信公众号样式优化
- 自动阴影和圆角效果

#### 接口定义
```typescript
interface ImageContainerProps {
  src: string;              // 图片路径（必需）
  alt: string;              // 图片描述（必需）
  width?: string | number;  // 宽度（默认：100%）
  height?: string | number; // 高度（默认：auto）
  caption?: string;         // 图片说明（可选）
}
```

#### 使用示例

**基础用法**
```typescript
<ImageContainer 
  src="/posts/2025-01-15/img/example.png" 
  alt="示例图片" 
/>
```

**带说明文字**
```typescript
<ImageContainer 
  src="/posts/2025-01-15/img/architecture.png" 
  alt="系统架构图" 
  caption="图1：系统整体架构设计"
/>
```

**自定义尺寸**
```typescript
<ImageContainer 
  src="/posts/2025-01-15/img/logo.png" 
  alt="公司Logo" 
  width="200px"
  height="100px"
  caption="公司标识"
/>
```

#### 最佳实践
- 图片路径必须以 `/posts/` 开头
- alt 文本应该描述图片内容，有助于 SEO
- caption 用于提供额外的图片说明
- 建议使用 PNG 或 JPG 格式

### 2. HighlightBox - 高亮提示框组件

#### 功能特性
- 四种预设样式类型
- 支持自定义标题
- 可包含任意 JSX 内容
- 微信公众号友好的颜色方案

#### 接口定义
```typescript
interface HighlightBoxProps {
  children: React.ReactNode;           // 内容（必需）
  type?: 'info' | 'warning' | 'success' | 'error'; // 类型（默认：info）
  title?: string;                      // 标题（可选）
}
```

#### 使用示例

**信息提示（蓝色）**
```typescript
<HighlightBox type="info" title="重要信息">
  <p>这是一个信息提示框，用于显示重要的补充信息。</p>
  <ul>
    <li>支持列表</li>
    <li>支持多种内容格式</li>
  </ul>
</HighlightBox>
```

**警告提示（黄色）**
```typescript
<HighlightBox type="warning">
  <p><strong>注意</strong>：在执行此操作前，请确保已备份重要数据。</p>
</HighlightBox>
```

**成功提示（绿色）**
```typescript
<HighlightBox type="success" title="操作成功">
  <p>配置已成功保存，系统将在 5 分钟内生效。</p>
</HighlightBox>
```

**错误提示（红色）**
```typescript
<HighlightBox type="error" title="错误">
  <p>连接失败，请检查网络设置后重试。</p>
</HighlightBox>
```

#### 样式说明
- **info**：蓝色边框，浅蓝背景，适合一般信息
- **warning**：黄色边框，浅黄背景，适合警告信息
- **success**：绿色边框，浅绿背景，适合成功信息
- **error**：红色边框，浅红背景，适合错误信息

### 3. CodeBlock - 代码块组件

#### 功能特性
- 语法高亮支持
- 可选代码标题
- 深色主题设计
- 代码复制友好

#### 接口定义
```typescript
interface CodeBlockProps {
  children: React.ReactNode;    // 代码内容（必需）
  language?: string;            // 编程语言（默认：text）
  title?: string;               // 代码标题（可选）
  showLineNumbers?: boolean;    // 显示行号（默认：false）
}
```

#### 使用示例

**JavaScript 代码**
```typescript
<CodeBlock language="javascript" title="用户认证函数">
{`function authenticateUser(username, password) {
  if (!username || !password) {
    throw new Error('用户名和密码不能为空');
  }
  
  return authService.login(username, password);
}`}
</CodeBlock>
```

**命令行示例**
```typescript
<CodeBlock language="bash" title="安装依赖">
{`# 安装项目依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build`}
</CodeBlock>
```

**TypeScript 接口**
```typescript
<CodeBlock language="typescript">
{`interface User {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

type UserRole = 'admin' | 'user' | 'guest';`}
</CodeBlock>
```

**JSON 配置**
```typescript
<CodeBlock language="json" title="配置文件">
{`{
  "name": "my-project",
  "version": "1.0.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build"
  }
}`}
</CodeBlock>
```

#### 支持的语言
- `javascript` / `js`
- `typescript` / `ts`
- `bash` / `shell`
- `json`
- `html`
- `css`
- `python`
- `java`
- `text`（默认）

### 4. Section - 内容分区组件

#### 功能特性
- 内容逻辑分组
- 统一的间距设计
- 可自定义样式

#### 接口定义
```typescript
interface SectionProps {
  children: React.ReactNode;  // 内容（必需）
  className?: string;         // 自定义样式类（可选）
  style?: React.CSSProperties; // 内联样式（可选）
}
```

#### 使用示例

**基础用法**
```typescript
<Section>
  <h2>技术背景</h2>
  <p>在现代 Web 开发中...</p>
  <ul>
    <li>性能优化</li>
    <li>用户体验</li>
  </ul>
</Section>
```

**自定义样式**
```typescript
<Section style={{ backgroundColor: '#f5f5f5', padding: '2rem' }}>
  <h3>特别说明</h3>
  <p>这个部分有特殊的背景色。</p>
</Section>
```

### 5. Divider - 分割线组件

#### 功能特性
- 视觉分隔内容
- 微信公众号适配样式
- 统一的间距

#### 接口定义
```typescript
interface DividerProps {
  style?: React.CSSProperties; // 自定义样式（可选）
}
```

#### 使用示例

**基础分割线**
```typescript
<Divider />
```

**自定义样式分割线**
```typescript
<Divider style={{ borderColor: '#ff6bb3', margin: '3rem 0' }} />
```

## 组件组合使用

### 完整文章结构示例

```typescript
export default function ArticlePage() {
  const metadata: ArticleMetadata = {
    title: '技术文章标题',
    date: '2025-01-15',
    excerpt: '文章摘要',
  };

  return (
    <ArticleLayout postMetadata={metadata}>
      <div className="article-content">
        <h1>文章标题</h1>
        
        <blockquote>
          <p>文章引言或摘要</p>
        </blockquote>

        <Section>
          <h2>背景介绍</h2>
          <p>技术背景说明...</p>
          
          <HighlightBox type="info" title="重要概念">
            <p>这里解释重要的技术概念。</p>
          </HighlightBox>
        </Section>

        <Divider />

        <Section>
          <h2>实现方案</h2>
          
          <ImageContainer 
            src="/posts/2025-01-15/img/architecture.png" 
            alt="系统架构图" 
            caption="图1：整体架构设计"
          />

          <CodeBlock language="typescript" title="核心代码">
{`interface Config {
  apiUrl: string;
  timeout: number;
}`}
          </CodeBlock>

          <HighlightBox type="warning">
            <p><strong>注意</strong>：请确保 API 配置正确。</p>
          </HighlightBox>
        </Section>

        <Divider />

        <Section>
          <h2>总结</h2>
          <p>文章总结...</p>
          
          <HighlightBox type="success" title="完成">
            <p>恭喜！您已经掌握了这项技术。</p>
          </HighlightBox>
        </Section>
      </div>
    </ArticleLayout>
  );
}
```

## 样式自定义

### 全局样式变量
组件使用的颜色和样式都经过微信公众号优化，如需自定义，可以通过以下方式：

```css
/* 在全局 CSS 中覆盖 */
.highlight-box {
  border-radius: 12px !important;
}

.code-block {
  font-size: 14px !important;
}
```

### 内联样式
```typescript
<HighlightBox 
  type="info" 
  style={{ borderRadius: '12px', padding: '2rem' }}
>
  <p>自定义样式的提示框</p>
</HighlightBox>
```

## 性能优化建议

1. **图片优化**：使用适当的图片格式和尺寸
2. **代码块**：避免过长的代码示例
3. **组件嵌套**：避免过深的组件嵌套
4. **内容分块**：使用 Section 组件合理分组内容

## 常见问题

### Q: 组件样式在微信中显示异常？
A: 确保使用组件库提供的组件，避免自定义 CSS 类名。

### Q: 图片在微信中不显示？
A: 检查图片路径是否正确，确保图片文件存在。

### Q: 代码块没有语法高亮？
A: 确认 language 属性设置正确，支持的语言列表见上文。

### Q: 如何添加新的组件类型？
A: 可以在 `src/app/posts/components/` 目录下创建新组件，并在 `index.ts` 中导出。
