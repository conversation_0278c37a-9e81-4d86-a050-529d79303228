# 快速参考模板

## 文章页面模板

### 基础模板
```typescript
'use client';

import React from 'react';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '../components';

export default function ArticlePage() {
  const metadata: ArticleMetadata = {
    title: '文章标题',
    date: 'YYYY-MM-DD',
    excerpt: '文章摘要（可选）',
  };

  return (
    <ArticleLayout metadata={metadata}>
      <div className="article-content">
        {/* 文章内容 */}
      </div>
    </ArticleLayout>
  );
}
```

### 技术教程模板
```typescript
'use client';

import React from 'react';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '../components';

export default function TechTutorialPage() {
  const metadata: ArticleMetadata = {
    title: '技术教程：[技术名称]',
    date: 'YYYY-MM-DD',
    excerpt: '本教程将介绍...',
  };

  return (
    <ArticleLayout metadata={metadata}>
      <div className="article-content">
        <h1>技术教程：[技术名称]</h1>
        
        <blockquote>
          <p>本教程将带你从零开始学习...</p>
        </blockquote>

        <Section>
          <h2>前置要求</h2>
          <HighlightBox type="info" title="需要掌握的技能">
            <ul>
              <li>基础的 JavaScript 知识</li>
              <li>了解 React 框架</li>
              <li>熟悉命令行操作</li>
            </ul>
          </HighlightBox>
        </Section>

        <Divider />

        <Section>
          <h2>环境准备</h2>
          <CodeBlock language="bash" title="安装依赖">
{`# 安装 Node.js 依赖
npm install package-name

# 启动开发环境
npm run dev`}
          </CodeBlock>
        </Section>

        <Divider />

        <Section>
          <h2>核心实现</h2>
          <CodeBlock language="typescript" title="核心代码">
{`// 在这里添加核心代码示例
interface Example {
  id: number;
  name: string;
}`}
          </CodeBlock>

          <HighlightBox type="warning">
            <p><strong>注意</strong>：请确保代码中的配置正确。</p>
          </HighlightBox>
        </Section>

        <Divider />

        <Section>
          <h2>总结</h2>
          <p>通过本教程，我们学习了...</p>
          
          <HighlightBox type="success" title="恭喜完成">
            <p>你已经成功掌握了这项技术！</p>
          </HighlightBox>
        </Section>
      </div>
    </ArticleLayout>
  );
}
```

### 产品介绍模板
```typescript
'use client';

import React from 'react';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '../components';

export default function ProductIntroPage() {
  const metadata: ArticleMetadata = {
    title: '产品介绍：[产品名称]',
    date: 'YYYY-MM-DD',
    excerpt: '[产品名称] 是一款...',
  };

  return (
    <ArticleLayout metadata={metadata}>
      <div className="article-content">
        <h1>产品介绍：[产品名称]</h1>
        
        <blockquote>
          <p>[产品名称] 是一款革命性的产品，旨在...</p>
        </blockquote>

        <ImageContainer 
          src="/posts/YYYY-MM-DD/img/product-cover.png" 
          alt="产品封面图" 
          caption="图1：产品主界面"
        />

        <Section>
          <h2>核心功能</h2>
          <ul>
            <li><strong>功能1</strong>：功能描述</li>
            <li><strong>功能2</strong>：功能描述</li>
            <li><strong>功能3</strong>：功能描述</li>
          </ul>

          <HighlightBox type="info" title="亮点功能">
            <p>这个产品的最大亮点是...</p>
          </HighlightBox>
        </Section>

        <Divider />

        <Section>
          <h2>使用场景</h2>
          <p>该产品适用于以下场景：</p>
          
          <ol>
            <li>场景一：描述</li>
            <li>场景二：描述</li>
            <li>场景三：描述</li>
          </ol>
        </Section>

        <Divider />

        <Section>
          <h2>技术优势</h2>
          <ImageContainer 
            src="/posts/YYYY-MM-DD/img/tech-architecture.png" 
            alt="技术架构图" 
            caption="图2：技术架构示意图"
          />

          <HighlightBox type="success" title="性能表现">
            <ul>
              <li>响应速度提升 50%</li>
              <li>内存占用减少 30%</li>
              <li>支持 10万+ 并发用户</li>
            </ul>
          </HighlightBox>
        </Section>
      </div>
    </ArticleLayout>
  );
}
```

## 常用组件代码片段

### 信息提示组合
```typescript
<HighlightBox type="info" title="💡 小贴士">
  <p>这里是有用的提示信息。</p>
</HighlightBox>

<HighlightBox type="warning" title="⚠️ 注意事项">
  <p>请注意这个重要的警告。</p>
</HighlightBox>

<HighlightBox type="success" title="✅ 成功">
  <p>操作已成功完成。</p>
</HighlightBox>

<HighlightBox type="error" title="❌ 错误">
  <p>发生了错误，请检查配置。</p>
</HighlightBox>
```

### 代码示例组合
```typescript
<CodeBlock language="bash" title="安装命令">
{`npm install package-name`}
</CodeBlock>

<CodeBlock language="javascript" title="基础用法">
{`import { Component } from 'package-name';

const app = new Component({
  option1: 'value1',
  option2: 'value2'
});`}
</CodeBlock>

<CodeBlock language="typescript" title="TypeScript 定义">
{`interface Config {
  apiUrl: string;
  timeout: number;
  retries?: number;
}`}
</CodeBlock>
```

### 图片展示组合
```typescript
<ImageContainer 
  src="/posts/YYYY-MM-DD/img/screenshot-1.png" 
  alt="功能截图" 
  caption="图1：主要功能界面"
/>

<ImageContainer 
  src="/posts/YYYY-MM-DD/img/diagram-1.png" 
  alt="流程图" 
  caption="图2：业务流程示意图"
  width="80%"
/>
```

## Markdown 到 TSX 转换对照表

| Markdown 语法 | TSX 转换 |
|---------------|----------|
| `# 标题` | `<h1>标题</h1>` |
| `## 二级标题` | `<h2>二级标题</h2>` |
| `**粗体**` | `<strong>粗体</strong>` |
| `*斜体*` | `<em>斜体</em>` |
| `` `代码` `` | `<code>代码</code>` |
| `> 引用` | `<blockquote><p>引用</p></blockquote>` |
| `- 列表项` | `<ul><li>列表项</li></ul>` |
| `1. 有序列表` | `<ol><li>有序列表</li></ol>` |
| `[链接](url)` | `<a href="url">链接</a>` |
| `![图片](url)` | `<ImageContainer src="url" alt="图片" />` |

## 快速替换规则

### VS Code 正则替换

**标题转换**
```
查找: ^# (.+)$
替换: <h1>$1</h1>

查找: ^## (.+)$
替换: <h2>$1</h2>

查找: ^### (.+)$
替换: <h3>$1</h3>
```

**列表转换**
```
查找: ^- (.+)$
替换: <li>$1</li>

查找: ^\d+\. (.+)$
替换: <li>$1</li>
```

**强调转换**
```
查找: \*\*(.+?)\*\*
替换: <strong>$1</strong>

查找: \*(.+?)\*
替换: <em>$1</em>

查找: `(.+?)`
替换: <code>$1</code>
```

## 文件创建清单

### 新文章创建步骤
1. [ ] 创建目录：`src/app/posts/YYYY-MM-DD/`
2. [ ] 创建文件：`src/app/posts/YYYY-MM-DD/page.tsx`
3. [ ] 创建图片目录：`public/posts/YYYY-MM-DD/img/`
4. [ ] 复制模板代码
5. [ ] 更新 metadata 信息
6. [ ] 转换 Markdown 内容
7. [ ] 添加图片资源
8. [ ] 本地预览测试
9. [ ] 复制到微信公众号

### 质量检查清单
- [ ] 文章标题和日期正确
- [ ] 所有图片路径正确
- [ ] 组件导入无错误
- [ ] TypeScript 编译通过
- [ ] 本地预览正常
- [ ] 微信公众号样式正确
- [ ] 所有链接可访问
- [ ] 代码块语法高亮正确

## 常见错误和解决方案

### 1. 组件导入错误
```typescript
// ❌ 错误
import { HighlightBox } from './components';

// ✅ 正确
import { HighlightBox } from '../components';
```

### 2. 图片路径错误
```typescript
// ❌ 错误
src="./img/image.png"
src="../img/image.png"

// ✅ 正确
src="/posts/2025-01-15/img/image.png"
```

### 3. 代码块内容错误
```typescript
// ❌ 错误 - 直接写代码
<CodeBlock language="javascript">
function example() {
  console.log('hello');
}
</CodeBlock>

// ✅ 正确 - 使用模板字符串
<CodeBlock language="javascript">
{`function example() {
  console.log('hello');
}`}
</CodeBlock>
```

### 4. metadata 类型错误
```typescript
// ❌ 错误 - 缺少必需字段
const metadata = {
  title: '标题'
};

// ✅ 正确 - 包含必需字段
const metadata: ArticleMetadata = {
  title: '标题',
  date: '2025-01-15',
  excerpt: '摘要' // 可选
};
```
