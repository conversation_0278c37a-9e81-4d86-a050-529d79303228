# 微信公众号文章创作工作流程优化技术方案

## 项目概述

本方案旨在优化现有的微信公众号文章创作工作流程，从手动创建TSX文件的方式升级为基于富文本编辑器的现代化内容管理系统，同时保持与现有组件体系和样式系统的完全兼容性。

## 现状分析

### 当前技术栈
- **框架**: Next.js 15.3.2 + TypeScript
- **样式**: Tailwind CSS v4 + SCSS + 赛博朋克主题
- **组件库**: shadcn/ui + 自定义WeChat组件
- **包管理**: pnpm
- **现有组件**: ImageContainer, HighlightBox, CodeBlock, Section, Divider
- **布局组件**: ArticleLayout（支持手机框预览、复制功能）

### 当前工作流程痛点
1. 每篇文章需手动创建TSX文件
2. Markdown到TSX转换工作量大
3. 缺乏可视化编辑体验
4. 内容管理分散，难以统一管理

## 技术方案设计

### 1. 富文本编辑器选择

**推荐方案: TipTap + 自定义扩展**

**选择理由:**
- 基于ProseMirror，架构稳定，扩展性强
- React集成良好，有官方@tiptap/react包
- 支持自定义节点，完美适配WeChat组件需求
- 支持Markdown导入导出
- 活跃的社区和完善的文档

**核心依赖包:**
```json
{
  "@tiptap/react": "^2.1.0",
  "@tiptap/starter-kit": "^2.1.0",
  "@tiptap/extension-image": "^2.1.0",
  "@tiptap/extension-code-block-lowlight": "^2.1.0",
  "lowlight": "^3.1.0"
}
```

### 2. 数据库设计

**推荐方案: SQLite + 文件系统混合存储**

**数据库表结构:**
```sql
-- 文章表
CREATE TABLE articles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  slug VARCHAR(50) UNIQUE NOT NULL,  -- YYYY-MM-DD格式
  title VARCHAR(255) NOT NULL,
  excerpt TEXT,
  content_json TEXT NOT NULL,        -- TipTap的JSON格式内容
  content_html TEXT,                 -- 渲染后的HTML（缓存）
  metadata_json TEXT,                -- 其他元数据
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  published_at DATETIME,
  status VARCHAR(20) DEFAULT 'draft' -- draft, published, archived
);

-- 图片资源表
CREATE TABLE article_images (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_slug VARCHAR(50) NOT NULL,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255),
  file_path VARCHAR(500) NOT NULL,
  file_size INTEGER,
  mime_type VARCHAR(100),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_slug) REFERENCES articles(slug)
);
```

**存储策略:**
- 文章内容: TipTap JSON格式存储到数据库
- 图片文件: 继续使用public/posts/目录
- HTML缓存: 预渲染HTML提高加载速度
- 向后兼容: 保留TSX文件读取能力

### 3. 系统架构设计

**新增目录结构:**
```
src/
├── app/
│   ├── admin/                    # 新增：管理后台
│   │   ├── articles/
│   │   │   ├── page.tsx         # 文章列表页
│   │   │   ├── new/page.tsx     # 新建文章页
│   │   │   └── [slug]/
│   │   │       ├── page.tsx     # 文章详情页
│   │   │       └── edit/page.tsx # 编辑文章页
│   │   └── layout.tsx           # 管理后台布局
│   ├── api/
│   │   ├── articles/            # 新增：文章API
│   │   │   ├── route.ts         # GET, POST
│   │   │   └── [slug]/route.ts  # GET, PUT, DELETE
│   │   └── upload/route.ts      # 新增：图片上传API
│   └── posts/[slug]/page.tsx    # 修改：支持动态渲染
├── components/
│   ├── editor/                  # 新增：编辑器组件
│   │   ├── TipTapEditor.tsx     # 主编辑器
│   │   ├── extensions/          # TipTap扩展
│   │   ├── toolbar/             # 工具栏组件
│   │   └── preview/             # 预览组件
│   └── admin/                   # 新增：管理界面组件
├── lib/
│   ├── database.ts              # 新增：数据库操作
│   ├── article-service.ts       # 新增：文章服务
│   └── tiptap-renderer.ts       # 新增：TipTap到React渲染器
└── types/
    └── article.ts               # 新增：类型定义
```

## 新工作流程设计

### 1. 内容创作阶段（保持不变）
- 作者在Gemini等工具中完成初稿
- 生成Markdown或HTML格式内容

### 2. 内容导入阶段（新增）
- 访问 `/admin/articles/new`
- 粘贴Markdown/HTML内容到导入区域
- 系统自动解析并转换为TipTap JSON格式
- 智能识别并转换为WeChat组件

### 3. 富文本编辑阶段（核心新功能）
- 使用TipTap编辑器进行可视化编辑
- 工具栏提供WeChat组件插入功能
- 实时预览编辑效果
- 支持图片拖拽上传
- 组件属性配置面板

### 4. 样式预览阶段（增强）
- 分屏显示：左侧编辑器，右侧预览
- 预览区域应用完整的赛博朋克样式
- 手机框预览模式
- 一键切换编辑/预览模式

### 5. 发布阶段（保持兼容）
- 保存为数据库记录
- 生成静态路由 `/posts/[slug]`
- 保持现有的"复制带样式内容"功能
- 继续支持微信公众号发布流程

## 组件兼容性保证

### 1. 现有组件保持不变
- ImageContainer, HighlightBox, CodeBlock, Section, Divider组件保持原有API
- 现有样式文件继续生效
- ArticleLayout组件继续作为文章容器

### 2. TipTap扩展映射
每个WeChat组件对应一个TipTap扩展：
- ImageContainerExtension
- HighlightBoxExtension
- CodeBlockExtension
- SectionExtension
- DividerExtension

### 3. 渐进式迁移策略
- **阶段1**: 新文章使用新系统，旧文章保持TSX格式
- **阶段2**: 提供迁移工具，将TSX文章转换为数据库格式
- **阶段3**: 完全切换到新系统，保留TSX读取能力作为备份

## 实施计划

### 第一阶段（核心功能）- 2-3周
1. 数据库设计和API开发
2. TipTap编辑器基础集成
3. 基础的WeChat组件扩展
4. 简单的管理界面

### 第二阶段（完善功能）- 1-2周
1. 完整的组件扩展
2. 图片上传功能
3. Markdown/HTML导入功能
4. 预览模式优化

### 第三阶段（优化和迁移）- 1周
1. 样式优化和移动端适配
2. 性能优化
3. 迁移工具开发
4. 文档更新

## 技术难点和解决方案

### 1. 样式一致性
**问题**: 编辑器预览与最终渲染样式不一致
**解决方案**: 
- 在编辑器中注入相同的SCSS样式
- 使用CSS-in-JS确保样式隔离
- 提供实时预览模式

### 2. 组件属性配置
**问题**: 复杂组件的属性配置界面设计
**解决方案**:
- 使用shadcn/ui组件构建配置面板
- 提供预设模板和自定义选项
- 实时预览属性变化

### 3. 图片路径管理
**问题**: 编辑器中的图片路径与发布后路径不一致
**解决方案**:
- 统一图片上传API
- 自动处理路径转换
- 保持现有的图片目录结构

## 预期效果

### 1. 效率提升
- 文章创建时间减少70%
- 无需手动编写TSX代码
- 可视化编辑提升体验

### 2. 内容管理
- 统一的文章管理界面
- 支持草稿、发布状态管理
- 便于内容检索和维护

### 3. 兼容性保证
- 现有文章无需修改
- 微信公众号发布流程不变
- 保持赛博朋克主题风格

## 风险评估

### 技术风险
- **低风险**: TipTap技术成熟，社区活跃
- **中风险**: 自定义扩展开发需要一定学习成本
- **缓解措施**: 分阶段实施，保持向后兼容

### 迁移风险
- **低风险**: 采用渐进式迁移策略
- **缓解措施**: 保留原有TSX文件读取能力

## 详细实施指南

### 1. 依赖包安装

```bash
# TipTap核心包
pnpm add @tiptap/react @tiptap/starter-kit @tiptap/pm
pnpm add @tiptap/extension-image @tiptap/extension-code-block-lowlight
pnpm add @tiptap/extension-table @tiptap/extension-table-row @tiptap/extension-table-cell @tiptap/extension-table-header

# 语法高亮
pnpm add lowlight

# 数据库
pnpm add better-sqlite3 drizzle-orm drizzle-kit
pnpm add -D @types/better-sqlite3

# 文件上传
pnpm add multer sharp
pnpm add -D @types/multer
```

### 2. 核心组件实现示例

#### TipTap编辑器组件
```typescript
// components/editor/TipTapEditor.tsx
'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { HighlightBoxExtension } from './extensions/HighlightBoxExtension';
import { ImageContainerExtension } from './extensions/ImageContainerExtension';
import { CodeBlockExtension } from './extensions/CodeBlockExtension';
import { SectionExtension } from './extensions/SectionExtension';
import { DividerExtension } from './extensions/DividerExtension';
import { Toolbar } from './toolbar/Toolbar';

interface TipTapEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  editable?: boolean;
}

export default function TipTapEditor({
  content = '',
  onChange,
  editable = true
}: TipTapEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      HighlightBoxExtension,
      ImageContainerExtension,
      CodeBlockExtension,
      SectionExtension,
      DividerExtension,
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
  });

  if (!editor) {
    return null;
  }

  return (
    <div className="tiptap-editor">
      {editable && <Toolbar editor={editor} />}
      <EditorContent
        editor={editor}
        className="prose prose-lg max-w-none focus:outline-none"
      />
    </div>
  );
}
```

#### HighlightBox扩展示例
```typescript
// components/editor/extensions/HighlightBoxExtension.ts
import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { HighlightBoxNodeView } from './HighlightBoxNodeView';

export const HighlightBoxExtension = Node.create({
  name: 'highlightBox',
  group: 'block',
  content: 'block+',

  addAttributes() {
    return {
      type: {
        default: 'info',
        parseHTML: element => element.getAttribute('data-type'),
        renderHTML: attributes => ({
          'data-type': attributes.type,
        }),
      },
      title: {
        default: '',
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => ({
          'data-title': attributes.title,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="highlight-box"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'highlight-box' }), 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(HighlightBoxNodeView);
  },
});
```

### 3. 数据库配置

#### Drizzle配置
```typescript
// lib/database/schema.ts
import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const articles = sqliteTable('articles', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  slug: text('slug').notNull().unique(),
  title: text('title').notNull(),
  excerpt: text('excerpt'),
  contentJson: text('content_json').notNull(),
  contentHtml: text('content_html'),
  metadataJson: text('metadata_json'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
  publishedAt: integer('published_at', { mode: 'timestamp' }),
  status: text('status').notNull().default('draft'),
});

export const articleImages = sqliteTable('article_images', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  articleSlug: text('article_slug').notNull(),
  filename: text('filename').notNull(),
  originalName: text('original_name'),
  filePath: text('file_path').notNull(),
  fileSize: integer('file_size'),
  mimeType: text('mime_type'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
});
```

### 4. API路由实现

#### 文章API
```typescript
// app/api/articles/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { articleService } from '@/lib/article-service';

export async function GET() {
  try {
    const articles = await articleService.getAllArticles();
    return NextResponse.json(articles);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch articles' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const article = await articleService.createArticle(data);
    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create article' }, { status: 500 });
  }
}
```

### 5. 管理界面实现

#### 文章列表页面
```typescript
// app/admin/articles/page.tsx
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Plus, Edit, Eye, Trash2 } from 'lucide-react';

interface Article {
  id: number;
  slug: string;
  title: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export default function ArticlesPage() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchArticles();
  }, []);

  const fetchArticles = async () => {
    try {
      const response = await fetch('/api/articles');
      const data = await response.json();
      setArticles(data);
    } catch (error) {
      console.error('Failed to fetch articles:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-6">加载中...</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">文章管理</h1>
        <Link href="/admin/articles/new">
          <Button className="bg-gradient-to-r from-cyan-500 to-blue-500">
            <Plus className="h-4 w-4 mr-2" />
            新建文章
          </Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                标题
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {articles.map((article) => (
              <tr key={article.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {article.title}
                  </div>
                  <div className="text-sm text-gray-500">
                    {article.slug}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    article.status === 'published'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {article.status === 'published' ? '已发布' : '草稿'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(article.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <Link href={`/posts/${article.slug}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/admin/articles/${article.slug}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button variant="outline" size="sm" className="text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
```

## 迁移工具设计

### TSX到数据库迁移脚本
```typescript
// scripts/migrate-tsx-articles.ts
import fs from 'fs';
import path from 'path';
import { articleService } from '@/lib/article-service';

async function migrateTsxArticles() {
  const postsDir = path.join(process.cwd(), 'src/app/posts');
  const directories = fs.readdirSync(postsDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
    .filter(name => /^\d{4}-\d{2}-\d{2}$/.test(name));

  for (const slug of directories) {
    const tsxPath = path.join(postsDir, slug, 'page.tsx');
    if (fs.existsSync(tsxPath)) {
      const tsxContent = fs.readFileSync(tsxPath, 'utf-8');

      // 解析TSX文件，提取metadata和内容
      const metadata = extractMetadata(tsxContent);
      const content = extractContent(tsxContent);

      // 转换为TipTap JSON格式
      const tiptapJson = convertToTiptapJson(content);

      // 保存到数据库
      await articleService.createArticle({
        slug,
        title: metadata.title,
        excerpt: metadata.excerpt,
        contentJson: JSON.stringify(tiptapJson),
        status: 'published'
      });

      console.log(`Migrated article: ${slug}`);
    }
  }
}

function extractMetadata(tsxContent: string) {
  // 实现TSX文件metadata提取逻辑
  // ...
}

function extractContent(tsxContent: string) {
  // 实现TSX文件内容提取逻辑
  // ...
}

function convertToTiptapJson(content: string) {
  // 实现内容转换为TipTap JSON格式的逻辑
  // ...
}
```

## 总结

本方案通过引入现代化的富文本编辑器，在保持现有组件体系和样式系统完全兼容的前提下，大幅提升文章创作效率和用户体验。采用渐进式迁移策略，确保系统升级的平滑过渡。

**关键优势:**
1. **效率提升**: 文章创建时间减少70%，无需手动编写TSX代码
2. **用户体验**: 可视化编辑，实时预览，组件化管理
3. **完全兼容**: 保持现有组件、样式和发布流程不变
4. **渐进迁移**: 新旧系统并存，平滑过渡
5. **技术先进**: 基于成熟的TipTap编辑器，扩展性强

**实施建议:**
- 优先实现核心功能，确保基本可用性
- 分阶段推进，降低技术风险
- 保持与现有系统的兼容性
- 提供完善的文档和培训
