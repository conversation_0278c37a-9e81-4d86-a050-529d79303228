# 微信公众号文章发布工作流程指南

## 概述

本指南详细介绍了从 Gemini 生成 Markdown 内容到微信公众号发布的完整工作流程。通过使用优化的 TSX + 组件库方案，我们可以高效地创建具有专业样式的微信公众号文章。

## 完整工作流程

### 第一步：内容创作（Gemini）

1. **在 Gemini 官网进行调研和撰写**
   - 使用 Gemini 进行技术调研
   - 生成 Markdown 格式的文章内容
   - 确保内容结构清晰，包含标题、段落、列表等

2. **保存原始内容**
   ```bash
   # 将 Markdown 内容保存到 content 目录
   content/YYYY-MM-DD.md
   ```

### 第二步：TSX 文件创建

1. **创建文章目录**
   ```bash
   mkdir src/app/posts/YYYY-MM-DD
   ```

2. **创建 page.tsx 文件**
   ```bash
   touch src/app/posts/YYYY-MM-DD/page.tsx
   ```

3. **使用标准模板**
   ```typescript
   'use client';

   import React from 'react';
   import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
   import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '../components';

   export default function ArticlePage() {
     const metadata: ArticleMetadata = {
       title: '文章标题',
       date: 'YYYY-MM-DD',
       excerpt: '文章摘要（可选）',
     };

     return (
       <ArticleLayout metadata={metadata}>
         <div className="article-content">
           {/* 文章内容将在这里添加 */}
         </div>
       </ArticleLayout>
     );
   }
   ```

### 第三步：Markdown 到 TSX 转换

#### 3.1 基本文本转换

| Markdown | TSX |
|----------|-----|
| `# 标题` | `<h1>标题</h1>` |
| `## 二级标题` | `<h2>二级标题</h2>` |
| `**粗体**` | `<strong>粗体</strong>` |
| `*斜体*` | `<em>斜体</em>` |
| `` `代码` `` | `<code>代码</code>` |

#### 3.2 段落和列表

```typescript
// Markdown 段落
// 这是一个段落

// TSX 转换
<p>这是一个段落</p>

// Markdown 列表
// - 项目1
// - 项目2

// TSX 转换
<ul>
  <li>项目1</li>
  <li>项目2</li>
</ul>
```

#### 3.3 引用块转换

```typescript
// Markdown
// > 这是一个引用

// TSX
<blockquote>
  <p>这是一个引用</p>
</blockquote>
```

### 第四步：组件库使用

#### 4.1 高亮提示框

```typescript
// 信息提示
<HighlightBox type="info" title="重要信息">
  <p>这里是信息内容</p>
</HighlightBox>

// 警告提示
<HighlightBox type="warning">
  <p>这里是警告内容</p>
</HighlightBox>

// 成功提示
<HighlightBox type="success" title="操作成功">
  <p>操作已成功完成</p>
</HighlightBox>

// 错误提示
<HighlightBox type="error">
  <p>发生了错误</p>
</HighlightBox>
```

#### 4.2 代码块

```typescript
// JavaScript 代码
<CodeBlock language="javascript" title="示例代码">
{`function example() {
  console.log('Hello World');
}`}
</CodeBlock>

// Bash 命令
<CodeBlock language="bash" title="安装命令">
{`npm install react
npm run dev`}
</CodeBlock>

// TypeScript 代码
<CodeBlock language="typescript">
{`interface User {
  id: number;
  name: string;
}`}
</CodeBlock>
```

#### 4.3 图片容器

```typescript
<ImageContainer 
  src="/posts/YYYY-MM-DD/img/image-name.png" 
  alt="图片描述" 
  caption="图片说明文字（可选）"
  width="100%"
  height="auto"
/>
```

#### 4.4 内容分区和分割线

```typescript
<Section>
  <h2>章节标题</h2>
  <p>章节内容...</p>
</Section>

<Divider />
```

### 第五步：图片资源管理

1. **创建图片目录**
   ```bash
   mkdir public/posts/YYYY-MM-DD/img
   ```

2. **图片命名规范**
   ```
   image-1.png    # 第一张图片
   image-2.jpg    # 第二张图片
   cover.png      # 封面图片
   diagram-1.svg  # 图表文件
   ```

3. **图片路径引用**
   ```typescript
   src="/posts/YYYY-MM-DD/img/image-1.png"
   ```

### 第六步：开发和预览

1. **启动开发服务器**
   ```bash
   pnpm dev
   ```

2. **预览文章**
   ```
   http://localhost:3002/posts/YYYY-MM-DD
   ```

3. **检查样式和功能**
   - 验证所有组件正确渲染
   - 测试响应式布局
   - 确认微信公众号样式

### 第七步：微信公众号发布

1. **复制带样式的 HTML**
   - 点击页面上的"复制带样式的内容"按钮
   - 系统会自动复制包含样式的 HTML 代码

2. **粘贴到微信公众号编辑器**
   - 打开微信公众号后台
   - 创建新文章
   - 直接粘贴复制的内容

3. **最终调整**
   - 检查格式是否正确
   - 调整图片大小（如需要）
   - 添加微信公众号特有的元素

## 文件结构规范

```
src/app/posts/
├── YYYY-MM-DD/
│   └── page.tsx           # 文章页面
├── components/            # 组件库
│   ├── ImageContainer.tsx
│   ├── HighlightBox.tsx
│   ├── CodeBlock.tsx
│   ├── Section.tsx
│   └── index.ts
└── ...

public/posts/
├── YYYY-MM-DD/
│   └── img/              # 图片资源
│       ├── image-1.png
│       └── cover.jpg
└── ...

content/
├── YYYY-MM-DD.md         # 原始 Markdown（备份）
└── ...
```

## 命名规范

### 文件命名
- **文章目录**：`YYYY-MM-DD` （如：`2025-01-15`）
- **页面文件**：`page.tsx`
- **图片文件**：`image-1.png`, `image-2.jpg`, `cover.png`

### 组件命名
- **函数名**：`ArticlePage`, `PostPage` 等
- **变量名**：`metadata`, `articleContent` 等

### 路径规范
- **文章路径**：`/posts/YYYY-MM-DD`
- **图片路径**：`/posts/YYYY-MM-DD/img/filename.ext`

## 质量检查清单

### 开发阶段
- [ ] TypeScript 编译无错误
- [ ] 所有组件正确导入
- [ ] 图片路径正确
- [ ] 响应式布局正常

### 发布前检查
- [ ] 文章标题和日期正确
- [ ] 所有链接可访问
- [ ] 图片加载正常
- [ ] 代码块语法高亮正确
- [ ] 微信公众号样式预览正常

### 发布后验证
- [ ] 微信公众号格式正确
- [ ] 图片在微信中正常显示
- [ ] 链接在微信中可点击
- [ ] 整体排版美观

## 常见问题解决

### 1. 图片不显示
```typescript
// 检查路径是否正确
src="/posts/2025-01-15/img/image-1.png"  // ✅ 正确
src="./img/image-1.png"                   // ❌ 错误
```

### 2. 组件导入错误
```typescript
// 正确的导入方式
import { HighlightBox } from '../components';

// 错误的导入方式
import { HighlightBox } from './components';
```

### 3. TypeScript 类型错误
```typescript
// 确保 metadata 包含必需字段
const metadata: ArticleMetadata = {
  title: '标题',        // 必需
  date: '2025-01-15',   // 必需
  excerpt: '摘要',      // 可选
};
```

## 效率提升技巧

1. **使用代码片段**：在 VS Code 中创建 TSX 模板片段
2. **批量处理**：使用查找替换功能快速转换 Markdown 语法
3. **组件复用**：优先使用组件库而不是自定义 HTML
4. **预览优先**：频繁预览确保效果符合预期

## 下一步优化方向

1. **自动化工具**：开发 Markdown 到 TSX 的转换脚本
2. **模板扩展**：创建更多专用组件
3. **样式优化**：持续改进微信公众号适配
4. **工作流程**：探索更高效的发布流程
