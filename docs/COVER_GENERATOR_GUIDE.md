# 弹窗式封面生成器使用指南

## 概述

新的弹窗式封面生成器解决了原有内联显示的问题，提供了更好的用户体验和更高质量的封面图片生成。

## 主要优化

### 1. 显示问题解决
- **弹窗模式**：提供更大的预览空间
- **标准比例**：严格按照微信公众号封面尺寸（900:383）显示
- **预览一致性**：确保预览效果与最终生成图片完全一致

### 2. 生成质量提升
- **高分辨率**：使用 2x scale 生成高清图片
- **字体优化**：使用 clamp() 函数实现响应式字体大小
- **图片处理**：优化背景图片的显示和处理

### 3. 响应式设计
- **移动端适配**：在手机上也能完美使用
- **灵活布局**：配置区域采用响应式网格布局
- **触摸友好**：按钮和输入框针对触摸操作优化

### 4. 智能字体大小
- **动态调整**：根据标题长度自动调整字体大小
- **分级显示**：短标题用大字体，长标题用小字体
- **实时反馈**：显示字符计数和字体大小提示
- **最佳显示**：确保任何长度的标题都能完美显示

## 使用方法

### 步骤 1：打开封面生成器
在文章页面侧边栏点击 "🎨 生成封面图片" 按钮

### 步骤 2：配置封面内容
1. **选择预设**：从下拉菜单选择预设模板
   - Cursor 系列
   - AI 工具
   - 编程技巧
   - 科技评测
   - 自定义

2. **自定义内容**：
   - 系列标签：显示在封面左上角的标识
   - 主标题：封面的主要标题
   - 副标题：补充说明文字
   - 背景图片：右侧显示的主题图片

### 步骤 3：预览效果
在弹窗中央的预览区域实时查看封面效果，支持：
- 实时更新：修改配置后立即看到变化
- 标准尺寸：按照实际使用比例显示
- 高质量预览：与最终生成图片完全一致

### 步骤 4：生成和复制
点击 "📋 复制封面图片" 按钮：
- 自动生成高质量 PNG 图片
- 直接复制到系统剪贴板
- 可直接粘贴到微信公众号或其他平台

## 技术特性

### 图片生成
- **尺寸**：900x383 像素（微信公众号标准）
- **分辨率**：2x scale，确保高清显示
- **格式**：PNG，支持透明背景
- **质量**：无损压缩，适合发布使用

### 样式系统
- **智能字体大小**：根据标题长度动态调整
  - 短标题（≤8字符）：28-36px 大字体
  - 中等标题（9-15字符）：24-32px 中字体
  - 长标题（16-25字符）：20-28px 小字体
  - 超长标题（>25字符）：18-24px 超小字体
- **渐变背景**：多层渐变营造科技感
- **网格装饰**：细节装饰元素增强视觉效果
- **品牌元素**：统一的品牌标识和配色

### 兼容性
- **浏览器支持**：现代浏览器（Chrome、Firefox、Safari、Edge）
- **移动设备**：iOS Safari、Android Chrome
- **剪贴板 API**：支持现代剪贴板操作

## 预设模板说明

### Cursor 系列
- 标签：CURSOR SERIES
- 主题：Cursor 编辑器相关内容
- 背景图：Cursor logo
- 适用：Cursor 使用技巧、功能介绍等

### AI 工具系列
- 标签：AI TOOLS
- 主题：AI 工具介绍和使用
- 背景图：AI 相关图标
- 适用：AI 工具评测、使用指南等

### 编程技巧系列
- 标签：PROGRAMMING
- 主题：编程相关技术分享
- 背景图：代码相关图标
- 适用：编程教程、技术分享等

### 科技评测系列
- 标签：TECH REVIEW
- 主题：科技产品评测
- 背景图：科技产品图标
- 适用：产品评测、技术分析等

## 故障排除

### 常见问题

1. **复制失败**
   - 检查浏览器是否支持剪贴板 API
   - 确保页面在 HTTPS 环境下运行
   - 尝试手动授权剪贴板权限

2. **图片显示异常**
   - 检查背景图片路径是否正确
   - 确保图片文件存在且可访问
   - 尝试使用其他格式的图片

3. **预览效果不佳**
   - 调整文字长度，避免过长标题
   - 选择合适的背景图片
   - 检查浏览器缩放比例

### 最佳实践

1. **标题长度**：主标题建议不超过 20 个字符
2. **副标题长度**：副标题建议不超过 40 个字符
3. **图片选择**：背景图片建议使用 PNG 格式，尺寸不超过 500KB
4. **预设使用**：优先使用预设模板，确保风格统一

## 更新日志

### v2.2 - 垂直居中修复
- **系列标签垂直居中修复**：彻底解决html2canvas生成图片时的垂直对齐问题
- **Flex布局优化**：容器和标签都使用完整的flex布局系统
- **样式一致性**：确保预览和生成图片的垂直对齐完全一致
- **布局稳定性**：添加lineHeight控制，避免字体基线影响对齐

### v2.1 - 字体和渲染优化
- **字体粗细优化**：主标题 fontWeight 从 900 调整为 750，显示更协调
- **html2canvas 兼容性修复**：解决系列标签在生成图片时的居中问题
- **渲染一致性**：确保复制的图片与网页预览效果完全一致
- **样式修复**：移除 html2canvas 不支持的 backdrop-filter 属性

### v2.0 - 弹窗式重构
- 重构为弹窗模式，解决预览空间问题
- 智能字体大小系统，根据标题长度动态调整
- 提升图片生成质量和响应式设计
- 改善移动端使用体验

### v1.0 - 初始版本
- 基础封面生成功能
- 内联显示模式
- 基本预设模板支持
