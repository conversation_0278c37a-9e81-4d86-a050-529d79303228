# 微信公众号文章工作流程优化 - 实施指南

## 概述

本指南提供了实施微信公众号文章工作流程优化的详细步骤，包括技术实现、代码示例和最佳实践。

## 第一阶段：基础设施搭建

### 1. 安装依赖包

```bash
# 进入项目目录
cd /Users/<USER>/WorkingPlace/Writing/SiliconBasedTeahouse2077

# 安装TipTap相关包
pnpm add @tiptap/react @tiptap/starter-kit @tiptap/pm
pnpm add @tiptap/extension-image @tiptap/extension-code-block-lowlight
pnpm add @tiptap/extension-table @tiptap/extension-table-row 
pnpm add @tiptap/extension-table-cell @tiptap/extension-table-header

# 安装语法高亮
pnpm add lowlight

# 安装数据库相关
pnpm add better-sqlite3 drizzle-orm drizzle-kit
pnpm add -D @types/better-sqlite3

# 安装文件上传相关
pnpm add multer sharp
pnpm add -D @types/multer

# 安装表单处理
pnpm add react-hook-form @hookform/resolvers zod
```

### 2. 创建目录结构

```bash
# 创建编辑器组件目录
mkdir -p src/components/editor/extensions
mkdir -p src/components/editor/toolbar
mkdir -p src/components/editor/preview

# 创建管理界面目录
mkdir -p src/components/admin
mkdir -p src/app/admin/articles/new
mkdir -p src/app/admin/articles/[slug]/edit

# 创建API目录
mkdir -p src/app/api/articles/[slug]
mkdir -p src/app/api/upload

# 创建数据库和服务目录
mkdir -p src/lib/database
mkdir -p src/lib/services

# 创建类型定义目录
mkdir -p src/types
```

### 3. 数据库配置

#### 创建数据库模式

```typescript
// src/lib/database/schema.ts
import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const articles = sqliteTable('articles', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  slug: text('slug').notNull().unique(),
  title: text('title').notNull(),
  excerpt: text('excerpt'),
  contentJson: text('content_json').notNull(),
  contentHtml: text('content_html'),
  metadataJson: text('metadata_json'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
  publishedAt: integer('published_at', { mode: 'timestamp' }),
  status: text('status').notNull().default('draft'),
});

export const articleImages = sqliteTable('article_images', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  articleSlug: text('article_slug').notNull(),
  filename: text('filename').notNull(),
  originalName: text('original_name'),
  filePath: text('file_path').notNull(),
  fileSize: integer('file_size'),
  mimeType: text('mime_type'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
});

export type Article = typeof articles.$inferSelect;
export type NewArticle = typeof articles.$inferInsert;
export type ArticleImage = typeof articleImages.$inferSelect;
export type NewArticleImage = typeof articleImages.$inferInsert;
```

#### 数据库连接配置

```typescript
// src/lib/database/connection.ts
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema';

const sqlite = new Database('articles.db');
export const db = drizzle(sqlite, { schema });

// 初始化数据库表
export function initDatabase() {
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS articles (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      slug TEXT UNIQUE NOT NULL,
      title TEXT NOT NULL,
      excerpt TEXT,
      content_json TEXT NOT NULL,
      content_html TEXT,
      metadata_json TEXT,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL,
      published_at INTEGER,
      status TEXT NOT NULL DEFAULT 'draft'
    );

    CREATE TABLE IF NOT EXISTS article_images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      article_slug TEXT NOT NULL,
      filename TEXT NOT NULL,
      original_name TEXT,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      created_at INTEGER NOT NULL
    );
  `);
}
```

### 4. 类型定义

```typescript
// src/types/article.ts
export interface ArticleMetadata {
  title: string;
  date: string;
  excerpt?: string;
  coverImage?: string;
  tags?: string[];
  author?: string;
}

export interface TipTapContent {
  type: string;
  content?: TipTapContent[];
  attrs?: Record<string, any>;
  text?: string;
}

export interface ArticleFormData {
  slug: string;
  title: string;
  excerpt?: string;
  content: TipTapContent;
  metadata?: Partial<ArticleMetadata>;
  status: 'draft' | 'published' | 'archived';
}

export interface WehatComponentProps {
  type: 'highlightBox' | 'imageContainer' | 'codeBlock' | 'section' | 'divider';
  attrs: Record<string, any>;
  content?: TipTapContent[];
}
```

## 第二阶段：核心组件开发

### 1. TipTap编辑器主组件

```typescript
// src/components/editor/TipTapEditor.tsx
'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { lowlight } from 'lowlight';

// 导入自定义扩展
import { HighlightBoxExtension } from './extensions/HighlightBoxExtension';
import { ImageContainerExtension } from './extensions/ImageContainerExtension';
import { SectionExtension } from './extensions/SectionExtension';
import { DividerExtension } from './extensions/DividerExtension';

// 导入工具栏
import { EditorToolbar } from './toolbar/EditorToolbar';

interface TipTapEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  editable?: boolean;
  className?: string;
}

export default function TipTapEditor({
  content,
  onChange,
  editable = true,
  className = ''
}: TipTapEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // 禁用默认代码块，使用自定义的
      }),
      Image,
      CodeBlockLowlight.configure({
        lowlight,
        defaultLanguage: 'javascript',
      }),
      HighlightBoxExtension,
      ImageContainerExtension,
      SectionExtension,
      DividerExtension,
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4',
      },
    },
  });

  if (!editor) {
    return <div className="animate-pulse bg-gray-200 h-96 rounded"></div>;
  }

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {editable && <EditorToolbar editor={editor} />}
      <div className="bg-white">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
}
```

### 2. HighlightBox扩展实现

```typescript
// src/components/editor/extensions/HighlightBoxExtension.ts
import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { HighlightBoxNodeView } from './HighlightBoxNodeView';

export const HighlightBoxExtension = Node.create({
  name: 'highlightBox',
  group: 'block',
  content: 'block+',
  
  addAttributes() {
    return {
      type: {
        default: 'info',
        parseHTML: element => element.getAttribute('data-type'),
        renderHTML: attributes => ({
          'data-type': attributes.type,
        }),
      },
      title: {
        default: '',
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => ({
          'data-title': attributes.title,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="highlight-box"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'highlight-box' }), 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(HighlightBoxNodeView);
  },

  addCommands() {
    return {
      setHighlightBox: (attributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: '在这里输入内容...',
                },
              ],
            },
          ],
        });
      },
    };
  },
});
```

### 3. HighlightBox节点视图

```typescript
// src/components/editor/extensions/HighlightBoxNodeView.tsx
import React from 'react';
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react';
import { HighlightBox } from '@/app/posts/components/HighlightBox';

interface HighlightBoxNodeViewProps {
  node: {
    attrs: {
      type: 'info' | 'warning' | 'success' | 'error';
      title: string;
    };
  };
  updateAttributes: (attrs: any) => void;
  selected: boolean;
}

export function HighlightBoxNodeView({ 
  node, 
  updateAttributes, 
  selected 
}: HighlightBoxNodeViewProps) {
  const { type, title } = node.attrs;

  return (
    <NodeViewWrapper className={`highlight-box-wrapper ${selected ? 'selected' : ''}`}>
      <div className="relative">
        {selected && (
          <div className="absolute -top-8 left-0 bg-white border rounded px-2 py-1 text-xs shadow-lg z-10">
            <select
              value={type}
              onChange={(e) => updateAttributes({ type: e.target.value })}
              className="mr-2"
            >
              <option value="info">信息</option>
              <option value="warning">警告</option>
              <option value="success">成功</option>
              <option value="error">错误</option>
            </select>
            <input
              type="text"
              value={title}
              onChange={(e) => updateAttributes({ title: e.target.value })}
              placeholder="标题（可选）"
              className="border rounded px-1"
            />
          </div>
        )}
        <HighlightBox type={type} title={title}>
          <NodeViewContent />
        </HighlightBox>
      </div>
    </NodeViewWrapper>
  );
}
```

## 第三阶段：API和服务层

### 1. 文章服务

```typescript
// src/lib/services/article-service.ts
import { db } from '@/lib/database/connection';
import { articles, articleImages } from '@/lib/database/schema';
import { eq, desc } from 'drizzle-orm';
import type { Article, NewArticle, ArticleFormData } from '@/types/article';

export class ArticleService {
  async getAllArticles(): Promise<Article[]> {
    return await db.select().from(articles).orderBy(desc(articles.createdAt));
  }

  async getArticleBySlug(slug: string): Promise<Article | null> {
    const result = await db.select().from(articles).where(eq(articles.slug, slug));
    return result[0] || null;
  }

  async createArticle(data: ArticleFormData): Promise<Article> {
    const now = new Date();
    const newArticle: NewArticle = {
      slug: data.slug,
      title: data.title,
      excerpt: data.excerpt,
      contentJson: JSON.stringify(data.content),
      contentHtml: this.renderTipTapToHtml(data.content),
      metadataJson: JSON.stringify(data.metadata || {}),
      status: data.status,
      createdAt: now,
      updatedAt: now,
    };

    const result = await db.insert(articles).values(newArticle).returning();
    return result[0];
  }

  async updateArticle(slug: string, data: Partial<ArticleFormData>): Promise<Article | null> {
    const updateData: Partial<NewArticle> = {
      ...data,
      updatedAt: new Date(),
    };

    if (data.content) {
      updateData.contentJson = JSON.stringify(data.content);
      updateData.contentHtml = this.renderTipTapToHtml(data.content);
    }

    if (data.metadata) {
      updateData.metadataJson = JSON.stringify(data.metadata);
    }

    const result = await db
      .update(articles)
      .set(updateData)
      .where(eq(articles.slug, slug))
      .returning();

    return result[0] || null;
  }

  async deleteArticle(slug: string): Promise<boolean> {
    const result = await db.delete(articles).where(eq(articles.slug, slug));
    return result.changes > 0;
  }

  private renderTipTapToHtml(content: any): string {
    // 这里实现TipTap JSON到HTML的转换
    // 可以使用@tiptap/html或自定义渲染器
    return JSON.stringify(content); // 临时实现
  }
}

export const articleService = new ArticleService();
```

### 2. 文章API路由

```typescript
// src/app/api/articles/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { articleService } from '@/lib/services/article-service';
import { initDatabase } from '@/lib/database/connection';

// 确保数据库已初始化
initDatabase();

export async function GET() {
  try {
    const articles = await articleService.getAllArticles();
    return NextResponse.json(articles);
  } catch (error) {
    console.error('Failed to fetch articles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 验证必需字段
    if (!data.slug || !data.title || !data.content) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const article = await articleService.createArticle(data);
    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    console.error('Failed to create article:', error);
    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}
```

## 第四阶段：管理界面

### 1. 文章编辑页面

```typescript
// src/app/admin/articles/new/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import TipTapEditor from '@/components/editor/TipTapEditor';
import { ArticlePreview } from '@/components/editor/preview/ArticlePreview';

export default function NewArticlePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    slug: '',
    title: '',
    excerpt: '',
    content: null,
    status: 'draft' as const,
  });
  const [isPreview, setIsPreview] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/articles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const article = await response.json();
        router.push(`/admin/articles/${article.slug}`);
      } else {
        throw new Error('Failed to save article');
      }
    } catch (error) {
      console.error('Error saving article:', error);
      alert('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  const generateSlug = (title: string) => {
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];
    return dateStr;
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">新建文章</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsPreview(!isPreview)}
          >
            {isPreview ? '编辑' : '预览'}
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? '保存中...' : '保存'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 编辑区域 */}
        <div className={isPreview ? 'hidden lg:block' : ''}>
          <div className="space-y-4 mb-6">
            <div>
              <Label htmlFor="title">文章标题</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => {
                  const title = e.target.value;
                  setFormData(prev => ({
                    ...prev,
                    title,
                    slug: generateSlug(title),
                  }));
                }}
                placeholder="输入文章标题"
              />
            </div>
            
            <div>
              <Label htmlFor="slug">文章路径</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="YYYY-MM-DD"
              />
            </div>

            <div>
              <Label htmlFor="excerpt">文章摘要</Label>
              <Textarea
                id="excerpt"
                value={formData.excerpt}
                onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                placeholder="输入文章摘要（可选）"
                rows={3}
              />
            </div>
          </div>

          <div>
            <Label>文章内容</Label>
            <TipTapEditor
              content={formData.content}
              onChange={(content) => setFormData(prev => ({ ...prev, content }))}
              className="mt-2"
            />
          </div>
        </div>

        {/* 预览区域 */}
        <div className={!isPreview ? 'hidden lg:block' : ''}>
          <ArticlePreview
            title={formData.title}
            content={formData.content}
            metadata={{
              title: formData.title,
              date: formData.slug,
              excerpt: formData.excerpt,
            }}
          />
        </div>
      </div>
    </div>
  );
}
```

## 总结

本实施指南提供了微信公众号文章工作流程优化的详细实现步骤。通过分阶段实施，可以确保系统的稳定性和可维护性。

**下一步行动:**
1. 按照第一阶段搭建基础设施
2. 实现核心的TipTap编辑器组件
3. 开发API和数据库服务
4. 构建管理界面
5. 测试和优化

**注意事项:**
- 保持与现有WeChat组件的兼容性
- 确保样式一致性
- 提供良好的用户体验
- 考虑性能优化
