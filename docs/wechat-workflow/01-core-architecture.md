# 01-核心架构设计

## 📋 概述

本文档详细描述微信公众号文章工作流程优化系统的核心架构设计，包括技术选型、系统分层、模块划分和数据流转。

## 🏗 系统架构概览

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  管理界面  │  编辑器界面  │  预览界面  │  发布界面           │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                 │
├─────────────────────────────────────────────────────────────┤
│  内容转换  │  组件映射  │  样式处理  │  发布管理             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  Prisma ORM  │  PostgreSQL  │  文件系统  │  缓存层          │
└─────────────────────────────────────────────────────────────┘
```

## 🛠 技术选型详解

### 前端技术栈

#### 1. Next.js 15.3.2 + TypeScript
**选择理由:**
- 与现有项目技术栈一致
- 支持SSR/SSG，提升SEO和性能
- API Routes简化后端开发
- 强类型支持，减少运行时错误

#### 2. TipTap 编辑器
**选择理由:**
- 基于ProseMirror，架构稳定
- React集成良好，支持自定义扩展
- 支持协作编辑（未来扩展）
- 活跃的社区和完善文档

**核心依赖:**
```json
{
  "@tiptap/react": "^2.1.0",
  "@tiptap/starter-kit": "^2.1.0",
  "@tiptap/extension-image": "^2.1.0",
  "@tiptap/extension-table": "^2.1.0",
  "@tiptap/html": "^2.1.0"
}
```

#### 3. 状态管理 - Zustand
**选择理由:**
- 轻量级，学习成本低
- TypeScript支持良好
- 无需Provider包装
- 适合中小型项目

### 后端技术栈

#### 1. Prisma + PostgreSQL
**选择理由:**
- 类型安全的数据库访问
- 自动生成类型定义
- 强大的迁移系统
- 支持关系查询和事务

#### 2. Next.js API Routes
**选择理由:**
- 与前端代码共享类型
- 简化部署流程
- 支持中间件和认证
- 易于测试和调试

## 📁 目录结构设计

```
src/
├── app/
│   ├── admin/                    # 管理后台
│   │   ├── articles/
│   │   │   ├── page.tsx         # 文章列表
│   │   │   ├── new/
│   │   │   │   └── page.tsx     # 新建文章
│   │   │   └── [slug]/
│   │   │       ├── page.tsx     # 文章详情
│   │   │       └── edit/
│   │   │           └── page.tsx # 编辑文章
│   │   └── layout.tsx           # 管理后台布局
│   ├── api/
│   │   ├── articles/            # 文章API
│   │   │   ├── route.ts         # CRUD操作
│   │   │   ├── [slug]/
│   │   │   │   └── route.ts     # 单篇文章操作
│   │   │   └── convert/
│   │   │       └── route.ts     # 内容转换API
│   │   ├── upload/              # 文件上传API
│   │   │   └── route.ts
│   │   └── export/              # 导出API
│   │       └── route.ts
│   └── posts/
│       └── [slug]/
│           └── page.tsx         # 动态文章页面
├── components/
│   ├── editor/                  # 编辑器组件
│   │   ├── TipTapEditor.tsx     # 主编辑器
│   │   ├── extensions/          # TipTap扩展
│   │   │   ├── HighlightBoxExtension.ts
│   │   │   ├── ImageContainerExtension.ts
│   │   │   ├── CodeBlockExtension.ts
│   │   │   ├── SectionExtension.ts
│   │   │   └── DividerExtension.ts
│   │   ├── toolbar/             # 工具栏组件
│   │   │   ├── Toolbar.tsx
│   │   │   └── ComponentPanel.tsx
│   │   ├── preview/             # 预览组件
│   │   │   └── ArticlePreview.tsx
│   │   └── converters/          # 转换器
│   │       ├── MarkdownConverter.ts
│   │       ├── HtmlConverter.ts
│   │       └── ComponentMapper.ts
│   ├── admin/                   # 管理界面组件
│   │   ├── ArticleList.tsx
│   │   ├── ArticleForm.tsx
│   │   ├── ImageUploader.tsx
│   │   └── ExportDialog.tsx
│   └── wechat/                  # 微信相关组件
│       ├── StyleInliner.tsx
│       ├── WechatPreview.tsx
│       └── CopyToClipboard.tsx
├── lib/
│   ├── prisma/                  # 数据库相关
│   │   ├── client.ts
│   │   └── migrations/
│   ├── services/                # 业务服务
│   │   ├── article-service.ts
│   │   ├── conversion-service.ts
│   │   ├── export-service.ts
│   │   └── upload-service.ts
│   ├── converters/              # 内容转换器
│   │   ├── markdown-to-tiptap.ts
│   │   ├── html-to-tiptap.ts
│   │   ├── tiptap-to-react.ts
│   │   └── style-processor.ts
│   ├── utils/                   # 工具函数
│   │   ├── slug-generator.ts
│   │   ├── image-processor.ts
│   │   └── validation.ts
│   └── constants/               # 常量定义
│       ├── component-mapping.ts
│       └── style-constants.ts
├── stores/                      # 状态管理
│   ├── article-store.ts
│   ├── editor-store.ts
│   └── ui-store.ts
├── types/                       # 类型定义
│   ├── article.ts
│   ├── editor.ts
│   ├── conversion.ts
│   └── wechat.ts
└── styles/                      # 样式文件
    ├── editor.scss              # 编辑器样式
    ├── admin.scss               # 管理界面样式
    └── wechat-export.scss       # 微信导出样式
```

## 🔄 数据流转设计

### 1. 内容创建流程
```
外部内容 → 粘贴导入 → 格式识别 → 组件转换 → TipTap JSON → 数据库存储
```

### 2. 编辑流程
```
数据库读取 → TipTap JSON → 编辑器渲染 → 用户编辑 → 实时保存 → 数据库更新
```

### 3. 预览流程
```
TipTap JSON → React组件渲染 → 样式应用 → 预览显示
```

### 4. 发布流程
```
TipTap JSON → HTML生成 → 样式内联 → 路径转换 → 微信编辑器格式
```

## 🧩 模块设计

### 1. 编辑器模块 (Editor Module)
**职责:**
- 提供富文本编辑功能
- 管理WeChat组件扩展
- 处理用户交互

**核心组件:**
- `TipTapEditor`: 主编辑器组件
- `ComponentExtensions`: WeChat组件扩展集合
- `Toolbar`: 编辑工具栏

### 2. 转换模块 (Conversion Module)
**职责:**
- HTML/Markdown到TipTap JSON转换
- TipTap JSON到React组件转换
- 样式处理和优化

**核心组件:**
- `MarkdownConverter`: Markdown转换器
- `HtmlConverter`: HTML转换器
- `ComponentMapper`: 组件映射器
- `StyleProcessor`: 样式处理器

### 3. 存储模块 (Storage Module)
**职责:**
- 数据持久化
- 文件管理
- 缓存处理

**核心组件:**
- `ArticleService`: 文章数据服务
- `UploadService`: 文件上传服务
- `CacheService`: 缓存服务

### 4. 导出模块 (Export Module)
**职责:**
- 微信编辑器格式生成
- 样式内联处理
- 兼容性优化

**核心组件:**
- `WechatExporter`: 微信导出器
- `StyleInliner`: 样式内联器
- `CompatibilityProcessor`: 兼容性处理器

## 🔧 扩展性设计

### 1. 组件扩展机制
```typescript
interface WehatComponentExtension {
  name: string;
  component: React.ComponentType;
  tiptapExtension: Extension;
  converter: ComponentConverter;
  validator: ComponentValidator;
}
```

### 2. 转换器扩展
```typescript
interface ContentConverter {
  name: string;
  supportedFormats: string[];
  convert: (input: string) => TipTapContent;
  validate: (input: string) => boolean;
}
```

### 3. 样式处理扩展
```typescript
interface StyleProcessor {
  name: string;
  process: (styles: CSSStyleSheet) => CSSStyleSheet;
  optimize: (styles: CSSStyleSheet) => CSSStyleSheet;
}
```

## 🔒 安全性设计

### 1. 输入验证
- HTML内容XSS防护
- 文件上传类型限制
- 数据格式验证

### 2. 权限控制
- 管理界面访问控制
- API接口权限验证
- 文件访问权限管理

### 3. 数据保护
- 敏感数据加密
- 数据备份机制
- 操作日志记录

## 📊 性能优化

### 1. 前端优化
- 组件懒加载
- 图片压缩和优化
- 代码分割

### 2. 后端优化
- 数据库查询优化
- 缓存策略
- API响应压缩

### 3. 存储优化
- 图片CDN加速
- 静态资源缓存
- 数据库索引优化

## 🔗 相关文档

- [02-数据库设计](./02-database-design.md) - 详细的数据模型设计
- [03-编辑器实现](./03-editor-implementation.md) - TipTap编辑器实现细节
- [06-内容转换机制](./06-content-conversion.md) - 内容转换核心机制
- [12-流程图集合](./12-workflow-diagrams.md) - 架构可视化图表

---

**下一步**: 阅读 [02-数据库设计](./02-database-design.md) 了解数据模型设计
