# 05-前端界面

## 📋 概述

本文档详细说明微信公众号文章工作流程优化系统的前端界面设计，包括管理界面、编辑器界面和用户交互体验。

## 🎯 设计原则

- **简洁高效**: 专注核心功能，避免复杂操作
- **响应式设计**: 支持桌面端和移动端访问
- **赛博朋克风格**: 保持与现有项目的视觉一致性
- **用户友好**: 直观的操作流程和清晰的反馈

## 🏗 界面架构

### 整体布局结构

```
┌─────────────────────────────────────────────────────────────┐
│                        顶部导航栏                            │
├─────────────────────────────────────────────────────────────┤
│  侧边栏  │                主内容区域                        │
│  导航    │  ┌─────────────────────────────────────────────┐  │
│         │  │            页面内容                        │  │
│  - 文章  │  │                                           │  │
│  - 设置  │  │                                           │  │
│  - 帮助  │  │                                           │  │
│         │  └─────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 📱 核心界面设计

### 1. 文章列表界面

```typescript
// src/app/admin/articles/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Plus, Edit, Eye, Trash2 } from 'lucide-react';

export default function ArticlesPage() {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto p-6">
        {/* 页面标题和操作 */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-pink-400 bg-clip-text text-transparent">
              文章管理
            </h1>
            <p className="text-slate-400 mt-2">管理您的微信公众号文章</p>
          </div>
          
          <Button 
            className="mt-4 md:mt-0 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
            onClick={() => router.push('/admin/articles/new')}
          >
            <Plus className="w-4 h-4 mr-2" />
            新建文章
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6 bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  placeholder="搜索文章标题或内容..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white"
                />
              </div>
              
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white"
              >
                <option value="ALL">全部状态</option>
                <option value="DRAFT">草稿</option>
                <option value="PUBLISHED">已发布</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* 文章列表 */}
        <div className="grid gap-4">
          {articles.map((article) => (
            <Card key={article.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-semibold text-white">{article.title}</h3>
                      <Badge 
                        variant={article.status === 'PUBLISHED' ? 'default' : 'secondary'}
                        className={article.status === 'PUBLISHED' 
                          ? 'bg-green-600 text-white' 
                          : 'bg-yellow-600 text-white'
                        }
                      >
                        {article.status === 'PUBLISHED' ? '已发布' : '草稿'}
                      </Badge>
                    </div>
                    
                    {article.excerpt && (
                      <p className="text-slate-300 mb-3 line-clamp-2">{article.excerpt}</p>
                    )}
                    
                    <div className="flex items-center gap-4 text-sm text-slate-400">
                      <span>路径: {article.slug}</span>
                      <span>更新: {new Date(article.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="border-red-600 text-red-400 hover:bg-red-600">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
```

### 2. 文章编辑界面

```typescript
// src/app/admin/articles/[id]/edit/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { WeChatEditor } from '@/components/editor/WeChatEditor';
import { ArticlePreview } from '@/components/editor/ArticlePreview';
import { Save, Eye, ArrowLeft } from 'lucide-react';

export default function EditArticlePage({ params }) {
  const [article, setArticle] = useState(null);
  const [isPreview, setIsPreview] = useState(false);
  const [saving, setSaving] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto p-6">
        {/* 顶部操作栏 */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" className="border-slate-600 text-slate-300">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回列表
            </Button>
            <h1 className="text-2xl font-bold text-white">编辑文章</h1>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsPreview(!isPreview)}
              className="border-slate-600 text-slate-300"
            >
              <Eye className="w-4 h-4 mr-2" />
              {isPreview ? '编辑' : '预览'}
            </Button>
            
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-gradient-to-r from-cyan-500 to-blue-500"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：基本信息 */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title" className="text-slate-300">文章标题</Label>
                  <Input
                    id="title"
                    value={article?.title || ''}
                    onChange={(e) => updateArticle('title', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                
                <div>
                  <Label htmlFor="slug" className="text-slate-300">文章路径</Label>
                  <Input
                    id="slug"
                    value={article?.slug || ''}
                    onChange={(e) => updateArticle('slug', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                
                <div>
                  <Label htmlFor="excerpt" className="text-slate-300">文章摘要</Label>
                  <Textarea
                    id="excerpt"
                    value={article?.excerpt || ''}
                    onChange={(e) => updateArticle('excerpt', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                    rows={4}
                  />
                </div>
                
                <div>
                  <Label htmlFor="status" className="text-slate-300">发布状态</Label>
                  <select
                    id="status"
                    value={article?.status || 'DRAFT'}
                    onChange={(e) => updateArticle('status', e.target.value)}
                    className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white"
                  >
                    <option value="DRAFT">草稿</option>
                    <option value="PUBLISHED">已发布</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：内容编辑/预览 */}
          <div className="lg:col-span-2">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">
                  {isPreview ? '文章预览' : '内容编辑'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isPreview ? (
                  <ArticlePreview content={article?.content} />
                ) : (
                  <WeChatEditor
                    content={article?.content}
                    onChange={(content) => updateArticle('content', content)}
                  />
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 3. 内容导入界面

```typescript
// src/components/editor/ContentImporter.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, FileText, Code } from 'lucide-react';

export function ContentImporter({ onImport }) {
  const [content, setContent] = useState('');
  const [importing, setImporting] = useState(false);
  const [importType, setImportType] = useState('auto');

  const handleImport = async () => {
    if (!content.trim()) return;
    
    setImporting(true);
    try {
      const response = await fetch('/api/convert/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          type: importType === 'auto' ? 'html' : importType,
          options: {
            autoDetectComponents: true,
            preserveFormatting: true,
          },
        }),
      });
      
      const result = await response.json();
      if (result.success) {
        onImport(result.data.tiptapJson);
        setContent('');
      }
    } catch (error) {
      console.error('导入失败:', error);
    } finally {
      setImporting(false);
    }
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Upload className="w-5 h-5" />
          内容导入
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            variant={importType === 'auto' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setImportType('auto')}
            className="border-slate-600"
          >
            自动识别
          </Button>
          <Button
            variant={importType === 'html' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setImportType('html')}
            className="border-slate-600"
          >
            <Code className="w-4 h-4 mr-1" />
            HTML
          </Button>
          <Button
            variant={importType === 'markdown' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setImportType('markdown')}
            className="border-slate-600"
          >
            <FileText className="w-4 h-4 mr-1" />
            Markdown
          </Button>
        </div>
        
        <Textarea
          placeholder="粘贴您的HTML或Markdown内容..."
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="min-h-[200px] bg-slate-700 border-slate-600 text-white font-mono text-sm"
        />
        
        <Button
          onClick={handleImport}
          disabled={!content.trim() || importing}
          className="w-full bg-gradient-to-r from-cyan-500 to-blue-500"
        >
          {importing ? '转换中...' : '导入并转换'}
        </Button>
      </CardContent>
    </Card>
  );
}
```

## 🎨 样式系统

### 赛博朋克主题样式

```scss
// src/styles/admin-theme.scss
.admin-interface {
  // 主色调
  --primary-cyan: #00d4ff;
  --primary-pink: #ff6bb3;
  --bg-dark: #0f172a;
  --bg-darker: #020617;
  --text-light: #e2e8f0;
  --text-muted: #64748b;

  // 渐变背景
  .cyberpunk-bg {
    background: linear-gradient(135deg, 
      var(--bg-dark) 0%, 
      #1e293b 25%, 
      #7c3aed 50%, 
      var(--bg-dark) 75%, 
      var(--bg-darker) 100%
    );
  }

  // 发光效果
  .glow-cyan {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }

  .glow-pink {
    box-shadow: 0 0 20px rgba(255, 107, 179, 0.3);
  }

  // 卡片样式
  .cyberpunk-card {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(100, 116, 139, 0.3);
    backdrop-filter: blur(10px);
    
    &:hover {
      border-color: var(--primary-cyan);
      @extend .glow-cyan;
    }
  }

  // 按钮样式
  .cyberpunk-button {
    background: linear-gradient(90deg, var(--primary-cyan), var(--primary-pink));
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      @extend .glow-cyan;
    }
  }
}
```

## 📱 响应式设计

```scss
// src/styles/responsive.scss
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
    
    .article-grid {
      grid-template-columns: 1fr;
    }
    
    .editor-layout {
      grid-template-columns: 1fr;
      
      .sidebar {
        order: 2;
        margin-top: 1rem;
      }
      
      .main-content {
        order: 1;
      }
    }
    
    .toolbar {
      flex-wrap: wrap;
      gap: 0.5rem;
      
      .button-group {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}
```

## 🔗 相关文档

- [03-编辑器实现](./03-editor-implementation.md) - 编辑器组件实现
- [04-API设计](./04-api-design.md) - 后端接口调用
- [07-样式一致性保证](./07-style-consistency.md) - 样式系统设计

---

**下一步**: 阅读 [10-部署指南](./10-deployment-guide.md) 了解生产环境部署
