# 10-部署指南

## 📋 概述

本文档提供微信公众号文章工作流程优化系统的生产环境部署指南，包括服务器配置、数据库设置、性能优化和监控方案。

## 🎯 部署架构

### 推荐部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡器                            │
│                     (Nginx/Cloudflare)                     │
├─────────────────────────────────────────────────────────────┤
│                      Next.js 应用                          │
│                    (Docker 容器)                           │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL 数据库  │  Redis 缓存  │  文件存储 (本地/云)    │
└─────────────────────────────────────────────────────────────┘
```

## 🐳 Docker 部署

### 1. Dockerfile

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# 复制包管理文件
COPY package.json pnpm-lock.yaml* ./
RUN corepack enable pnpm && pnpm i --frozen-lockfile

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 生成 Prisma 客户端
RUN npx prisma generate

# 构建 Next.js 应用
ENV NEXT_TELEMETRY_DISABLED 1
RUN pnpm build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/wechat_workflow
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_SECRET=your-secret-key
      - NEXTAUTH_URL=https://your-domain.com
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/public/uploads
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=wechat_workflow
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## 🌐 Nginx 配置

### 反向代理配置

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream nextjs {
        server app:3000;
    }

    # 限制请求大小（支持大图片上传）
    client_max_body_size 50M;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    server {
        listen 80;
        server_name your-domain.com;
        
        # 重定向到 HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL 配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # 静态文件缓存
        location /_next/static/ {
            proxy_pass http://nextjs;
            proxy_cache_valid 200 1y;
            add_header Cache-Control "public, immutable";
        }

        # 图片文件缓存
        location /uploads/ {
            proxy_pass http://nextjs;
            proxy_cache_valid 200 30d;
            add_header Cache-Control "public";
        }

        # API 路由
        location /api/ {
            proxy_pass http://nextjs;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 主应用
        location / {
            proxy_pass http://nextjs;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 🗄 数据库配置

### PostgreSQL 优化

```sql
-- init.sql
-- 创建数据库和用户
CREATE DATABASE wechat_workflow;
CREATE USER wechat_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE wechat_workflow TO wechat_user;

-- 性能优化配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 重新加载配置
SELECT pg_reload_conf();
```

### 数据库备份脚本

```bash
#!/bin/bash
# backup.sh

# 配置
DB_NAME="wechat_workflow"
DB_USER="postgres"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker exec postgres_container pg_dump -U $DB_USER -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "备份完成: backup_$DATE.sql.gz"
```

## 🚀 部署脚本

### 自动化部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

echo "开始部署微信工作流程系统..."

# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
pnpm install --frozen-lockfile

# 3. 生成 Prisma 客户端
npx prisma generate

# 4. 运行数据库迁移
npx prisma db push

# 5. 构建应用
pnpm build

# 6. 重启 Docker 容器
docker-compose down
docker-compose up -d --build

# 7. 等待服务启动
echo "等待服务启动..."
sleep 30

# 8. 健康检查
if curl -f http://localhost:3000/api/health; then
    echo "✅ 部署成功！"
else
    echo "❌ 部署失败，请检查日志"
    docker-compose logs app
    exit 1
fi

echo "🎉 部署完成！"
```

## 📊 监控和日志

### 健康检查端点

```typescript
// src/app/api/health/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET() {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    // 检查磁盘空间
    const stats = await import('fs').then(fs => fs.promises.stat('./'));
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
```

### 日志配置

```typescript
// src/lib/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

export default logger;
```

## 🔒 安全配置

### 环境变量

```bash
# .env.production
# 数据库
DATABASE_URL="postgresql://user:password@localhost:5432/wechat_workflow"

# 认证
NEXTAUTH_SECRET="your-very-secure-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# Redis
REDIS_URL="redis://localhost:6379"

# 文件上传
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp"

# 安全
CORS_ORIGIN="https://your-domain.com"
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
```

### 安全中间件

```typescript
// src/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { rateLimit } from '@/lib/rate-limit';

export async function middleware(request: NextRequest) {
  // 速率限制
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const rateLimitResult = await rateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: '请求过于频繁，请稍后再试' },
        { status: 429 }
      );
    }
  }

  // CORS 设置
  const response = NextResponse.next();
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  return response;
}

export const config = {
  matcher: ['/api/:path*', '/admin/:path*'],
};
```

## 📈 性能优化

### Next.js 配置

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },
  images: {
    domains: ['localhost', 'your-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  
  // 缓存配置
  async headers() {
    return [
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

## 🔗 相关文档

- [09-实施指南](./09-implementation-guide.md) - 开发环境配置
- [11-故障排除](./11-troubleshooting.md) - 常见问题解决
- [01-核心架构设计](./01-core-architecture.md) - 系统架构参考

---

**部署完成后**: 参考 [11-故障排除](./11-troubleshooting.md) 解决可能遇到的问题
