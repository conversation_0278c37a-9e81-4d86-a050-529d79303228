# 09-实施指南

## 📋 概述

本文档提供微信公众号文章工作流程优化系统的分阶段实施计划，专注于核心功能的快速部署和用户体验优化。

## 🎯 实施原则

### 简化优先
- **核心功能优先**: 专注于文章创建、编辑、预览、发布四大核心功能
- **界面简洁**: 移除复杂的高级功能，保持界面清晰易用
- **用户体验**: 优先考虑用户操作的便捷性和直观性

### 渐进式部署
- **阶段性实施**: 分三个阶段逐步部署功能
- **向后兼容**: 确保与现有系统的完全兼容
- **风险控制**: 每个阶段都有回滚方案

## 🚀 第一阶段：基础设施（1-2周）

### 1.1 环境准备

```bash
# 1. 安装核心依赖
pnpm add @tiptap/react @tiptap/starter-kit @tiptap/pm
pnpm add @tiptap/extension-image @tiptap/html
pnpm add prisma @prisma/client
pnpm add zustand

# 2. 安装开发依赖
pnpm add -D prisma
```

### 1.2 数据库设置

```bash
# 1. 初始化Prisma
npx prisma init

# 2. 配置环境变量
echo "DATABASE_URL=\"postgresql://username:password@localhost:5432/wechat_workflow\"" >> .env

# 3. 创建数据库模式
npx prisma db push

# 4. 生成客户端
npx prisma generate
```

### 1.3 基础目录结构

```bash
# 创建核心目录
mkdir -p src/components/editor
mkdir -p src/app/admin/articles
mkdir -p src/lib/services
mkdir -p src/stores
mkdir -p src/types
```

### 1.4 核心类型定义

```typescript
// src/types/article.ts
export interface Article {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  contentJson: string;
  status: 'DRAFT' | 'PUBLISHED';
  createdAt: Date;
  updatedAt: Date;
}

export interface ArticleFormData {
  slug: string;
  title: string;
  excerpt?: string;
  content: any; // TipTap JSON
  status: 'DRAFT' | 'PUBLISHED';
}
```

## 🎨 第二阶段：核心编辑器（2-3周）

### 2.1 简化的TipTap编辑器

```typescript
// src/components/editor/SimpleEditor.tsx
'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { Button } from '@/components/ui/button';

interface SimpleEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  editable?: boolean;
}

export function SimpleEditor({ content, onChange, editable = true }: SimpleEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4',
      },
    },
  });

  if (!editor) return null;

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
      {editable && (
        <div className="border-b border-gray-300 p-2 flex gap-2 flex-wrap bg-gray-50">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive('bold') ? 'bg-gray-200' : ''}
          >
            粗体
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'bg-gray-200' : ''}
          >
            斜体
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}
          >
            标题
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
          >
            列表
          </Button>
        </div>
      )}
      
      <EditorContent editor={editor} />
    </div>
  );
}
```

### 2.2 状态管理

```typescript
// src/stores/article-store.ts
import { create } from 'zustand';
import { Article, ArticleFormData } from '@/types/article';

interface ArticleStore {
  articles: Article[];
  currentArticle: Article | null;
  loading: boolean;
  
  // Actions
  setArticles: (articles: Article[]) => void;
  addArticle: (article: Article) => void;
  updateArticle: (id: string, data: Partial<Article>) => void;
  deleteArticle: (id: string) => void;
  setCurrentArticle: (article: Article | null) => void;
  setLoading: (loading: boolean) => void;
}

export const useArticleStore = create<ArticleStore>((set, get) => ({
  articles: [],
  currentArticle: null,
  loading: false,

  setArticles: (articles) => set({ articles }),
  
  addArticle: (article) => set((state) => ({
    articles: [article, ...state.articles],
  })),

  updateArticle: (id, data) => set((state) => ({
    articles: state.articles.map((article) =>
      article.id === id ? { ...article, ...data } : article
    ),
  })),

  deleteArticle: (id) => set((state) => ({
    articles: state.articles.filter((article) => article.id !== id),
  })),

  setCurrentArticle: (article) => set({ currentArticle: article }),
  
  setLoading: (loading) => set({ loading }),
}));
```

### 2.3 文章服务

```typescript
// src/lib/services/article-service.ts
import { prisma } from '@/lib/prisma/client';
import { Article, ArticleFormData } from '@/types/article';

export class ArticleService {
  async getAllArticles(): Promise<Article[]> {
    return await prisma.article.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  async getArticleBySlug(slug: string): Promise<Article | null> {
    return await prisma.article.findUnique({
      where: { slug },
    });
  }

  async createArticle(data: ArticleFormData): Promise<Article> {
    return await prisma.article.create({
      data: {
        slug: data.slug,
        title: data.title,
        excerpt: data.excerpt,
        contentJson: JSON.stringify(data.content),
        status: data.status,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  async updateArticle(id: string, data: Partial<ArticleFormData>): Promise<Article> {
    const updateData: any = {
      ...data,
      updatedAt: new Date(),
    };

    if (data.content) {
      updateData.contentJson = JSON.stringify(data.content);
    }

    return await prisma.article.update({
      where: { id },
      data: updateData,
    });
  }

  async deleteArticle(id: string): Promise<boolean> {
    try {
      await prisma.article.delete({
        where: { id },
      });
      return true;
    } catch {
      return false;
    }
  }
}

export const articleService = new ArticleService();
```

## 🖥 第三阶段：管理界面（1-2周）

### 3.1 简化的文章列表

```typescript
// src/app/admin/articles/page.tsx
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useArticleStore } from '@/stores/article-store';
import { articleService } from '@/lib/services/article-service';

export default function ArticlesPage() {
  const { articles, setArticles, loading, setLoading } = useArticleStore();

  useEffect(() => {
    loadArticles();
  }, []);

  const loadArticles = async () => {
    setLoading(true);
    try {
      const data = await articleService.getAllArticles();
      setArticles(data);
    } catch (error) {
      console.error('加载文章失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-6">加载中...</div>;
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">文章管理</h1>
        <Link href="/admin/articles/new">
          <Button className="bg-gradient-to-r from-cyan-500 to-blue-500">
            新建文章
          </Button>
        </Link>
      </div>

      {articles.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">还没有文章</p>
          <Link href="/admin/articles/new">
            <Button>创建第一篇文章</Button>
          </Link>
        </div>
      ) : (
        <div className="grid gap-4">
          {articles.map((article) => (
            <div key={article.id} className="bg-white p-6 rounded-lg shadow border">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">{article.title}</h3>
                  {article.excerpt && (
                    <p className="text-gray-600 mb-2">{article.excerpt}</p>
                  )}
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>路径: {article.slug}</span>
                    <span>状态: {article.status === 'PUBLISHED' ? '已发布' : '草稿'}</span>
                    <span>更新: {new Date(article.updatedAt).toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Link href={`/posts/${article.slug}`}>
                    <Button variant="outline" size="sm">预览</Button>
                  </Link>
                  <Link href={`/admin/articles/${article.id}/edit`}>
                    <Button variant="outline" size="sm">编辑</Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 3.2 简化的文章编辑器

```typescript
// src/app/admin/articles/new/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SimpleEditor } from '@/components/editor/SimpleEditor';
import { articleService } from '@/lib/services/article-service';

export default function NewArticlePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    slug: '',
    title: '',
    excerpt: '',
    content: null,
    status: 'DRAFT' as const,
  });
  const [saving, setSaving] = useState(false);

  const generateSlug = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  const handleSave = async () => {
    if (!formData.title || !formData.slug) {
      alert('请填写标题和路径');
      return;
    }

    setSaving(true);
    try {
      const article = await articleService.createArticle(formData);
      router.push(`/admin/articles`);
    } catch (error) {
      console.error('保存失败:', error);
      alert('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">新建文章</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? '保存中...' : '保存'}
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {/* 基本信息 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">基本信息</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">文章标题 *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => {
                  const title = e.target.value;
                  setFormData(prev => ({
                    ...prev,
                    title,
                    slug: prev.slug || generateSlug(),
                  }));
                }}
                placeholder="输入文章标题"
              />
            </div>
            
            <div>
              <Label htmlFor="slug">文章路径 *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="YYYY-MM-DD"
              />
            </div>
          </div>
          
          <div className="mt-4">
            <Label htmlFor="excerpt">文章摘要</Label>
            <Textarea
              id="excerpt"
              value={formData.excerpt}
              onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
              placeholder="输入文章摘要（可选）"
              rows={3}
            />
          </div>
        </div>

        {/* 内容编辑 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">文章内容</h2>
          <SimpleEditor
            content={formData.content}
            onChange={(content) => setFormData(prev => ({ ...prev, content }))}
          />
        </div>
      </div>
    </div>
  );
}
```

## 🔧 API路由实现

### API路由

```typescript
// src/app/api/articles/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { articleService } from '@/lib/services/article-service';

export async function GET() {
  try {
    const articles = await articleService.getAllArticles();
    return NextResponse.json(articles);
  } catch (error) {
    return NextResponse.json(
      { error: '获取文章列表失败' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const article = await articleService.createArticle(data);
    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: '创建文章失败' },
      { status: 500 }
    );
  }
}
```

## 📱 响应式优化

### 移动端适配

```scss
// src/styles/admin.scss
.admin-container {
  @media (max-width: 768px) {
    padding: 1rem;
    
    .article-grid {
      grid-template-columns: 1fr;
    }
    
    .editor-toolbar {
      flex-wrap: wrap;
      gap: 0.5rem;
      
      button {
        font-size: 0.875rem;
        padding: 0.5rem;
      }
    }
    
    .form-grid {
      grid-template-columns: 1fr;
    }
  }
}
```

## 🚀 部署准备

### 1. 环境变量配置

```bash
# .env.production
DATABASE_URL="postgresql://prod_user:prod_pass@localhost:5432/wechat_workflow_prod"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://your-domain.com"
```

### 2. 构建脚本

```json
{
  "scripts": {
    "build": "prisma generate && next build",
    "start": "next start",
    "deploy": "npm run build && pm2 restart wechat-workflow"
  }
}
```

## 📊 成功指标

### 用户体验指标
- **文章创建时间**: 目标 < 10分钟
- **界面响应时间**: 目标 < 2秒
- **错误率**: 目标 < 1%

### 技术指标
- **页面加载时间**: 目标 < 3秒
- **数据库查询时间**: 目标 < 500ms
- **内存使用**: 目标 < 512MB

## 🔗 相关文档

- [01-核心架构设计](./01-core-architecture.md) - 系统架构参考
- [02-数据库设计](./02-database-design.md) - 数据库实施细节
- [11-故障排除](./11-troubleshooting.md) - 实施过程问题解决

---

**下一步**: 按照本指南的三个阶段逐步实施，每个阶段完成后进行测试验证再进入下一阶段。
