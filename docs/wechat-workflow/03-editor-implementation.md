# 03-编辑器实现

## 📋 概述

本文档详细说明TipTap编辑器的实现方案，包括WeChat组件扩展、自定义节点和编辑器配置。

## 🎯 核心目标

- **组件集成**: 将现有WeChat组件集成到TipTap编辑器
- **可视化编辑**: 提供所见即所得的编辑体验
- **样式一致性**: 确保编辑器预览与最终显示一致
- **扩展性**: 支持未来新组件的快速集成

## 🛠 技术架构

### 编辑器核心配置

```typescript
// src/components/editor/WeChatEditor.tsx
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { ImageContainerExtension } from './extensions/ImageContainerExtension';
import { HighlightBoxExtension } from './extensions/HighlightBoxExtension';
import { CodeBlockExtension } from './extensions/CodeBlockExtension';
import { SectionExtension } from './extensions/SectionExtension';
import { DividerExtension } from './extensions/DividerExtension';

export function WeChatEditor({ content, onChange, editable = true }) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // 使用自定义CodeBlock
      }),
      ImageContainerExtension,
      HighlightBoxExtension,
      CodeBlockExtension,
      SectionExtension,
      DividerExtension,
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 cyberpunk-editor',
      },
    },
  });

  return (
    <div className="wechat-editor">
      <EditorToolbar editor={editor} />
      <EditorContent editor={editor} />
    </div>
  );
}
```

## 🧩 WeChat组件扩展

### 1. ImageContainer扩展

```typescript
// src/components/editor/extensions/ImageContainerExtension.ts
import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { ImageContainerNodeView } from './ImageContainerNodeView';

export const ImageContainerExtension = Node.create({
  name: 'imageContainer',
  group: 'block',
  atom: true,

  addAttributes() {
    return {
      src: { default: '' },
      alt: { default: '' },
      width: { default: '100%' },
      height: { default: 'auto' },
      caption: { default: '' },
    };
  },

  parseHTML() {
    return [{ tag: 'image-container' }];
  },

  renderHTML({ HTMLAttributes }) {
    return ['image-container', mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(ImageContainerNodeView);
  },

  addCommands() {
    return {
      setImageContainer: (attributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
        });
      },
    };
  },
});
```

### 2. ImageContainer节点视图

```typescript
// src/components/editor/extensions/ImageContainerNodeView.tsx
import React, { useState } from 'react';
import { NodeViewWrapper } from '@tiptap/react';
import { ImageContainer } from '@/app/posts/components/ImageContainer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export function ImageContainerNodeView({ node, updateAttributes, selected }) {
  const [isEditing, setIsEditing] = useState(false);
  const { src, alt, width, height, caption } = node.attrs;

  const handleUpdate = (field: string, value: string) => {
    updateAttributes({ [field]: value });
  };

  return (
    <NodeViewWrapper className={`image-container-node ${selected ? 'selected' : ''}`}>
      {isEditing ? (
        <div className="p-4 border border-blue-300 rounded-lg bg-blue-50">
          <div className="space-y-3">
            <div>
              <Label htmlFor="src">图片地址</Label>
              <Input
                id="src"
                value={src}
                onChange={(e) => handleUpdate('src', e.target.value)}
                placeholder="输入图片URL"
              />
            </div>
            
            <div>
              <Label htmlFor="alt">图片描述</Label>
              <Input
                id="alt"
                value={alt}
                onChange={(e) => handleUpdate('alt', e.target.value)}
                placeholder="输入图片描述"
              />
            </div>
            
            <div>
              <Label htmlFor="caption">图片说明</Label>
              <Input
                id="caption"
                value={caption}
                onChange={(e) => handleUpdate('caption', e.target.value)}
                placeholder="输入图片说明（可选）"
              />
            </div>
            
            <div className="flex gap-2">
              <Button size="sm" onClick={() => setIsEditing(false)}>
                完成
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="relative group">
          <ImageContainer
            src={src}
            alt={alt}
            width={width}
            height={height}
            caption={caption}
          />
          
          {selected && (
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(true)}
                className="bg-white"
              >
                编辑
              </Button>
            </div>
          )}
        </div>
      )}
    </NodeViewWrapper>
  );
}
```

### 3. HighlightBox扩展

```typescript
// src/components/editor/extensions/HighlightBoxExtension.ts
export const HighlightBoxExtension = Node.create({
  name: 'highlightBox',
  group: 'block',
  content: 'block+',

  addAttributes() {
    return {
      type: {
        default: 'info',
        parseHTML: element => element.getAttribute('data-type'),
        renderHTML: attributes => ({ 'data-type': attributes.type }),
      },
      title: {
        default: '',
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => ({ 'data-title': attributes.title }),
      },
    };
  },

  parseHTML() {
    return [{ tag: 'highlight-box' }];
  },

  renderHTML({ HTMLAttributes }) {
    return ['highlight-box', mergeAttributes(HTMLAttributes), 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(HighlightBoxNodeView);
  },

  addCommands() {
    return {
      setHighlightBox: (attributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
          content: [
            {
              type: 'paragraph',
              content: [{ type: 'text', text: '在这里输入内容...' }],
            },
          ],
        });
      },
    };
  },
});
```

## 🎨 编辑器工具栏

```typescript
// src/components/editor/EditorToolbar.tsx
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export function EditorToolbar({ editor }) {
  if (!editor) return null;

  return (
    <div className="border-b border-gray-300 p-2 flex gap-2 flex-wrap bg-gray-50">
      {/* 基础格式 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleBold().run()}
        className={editor.isActive('bold') ? 'bg-gray-200' : ''}
      >
        粗体
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleItalic().run()}
        className={editor.isActive('italic') ? 'bg-gray-200' : ''}
      >
        斜体
      </Button>

      <Separator orientation="vertical" className="h-6" />

      {/* 标题 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        className={editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}
      >
        标题
      </Button>

      <Separator orientation="vertical" className="h-6" />

      {/* WeChat组件 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().setImageContainer({
          src: '/placeholder.jpg',
          alt: '图片描述',
        }).run()}
      >
        图片容器
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().setHighlightBox({
          type: 'info',
          title: '提示',
        }).run()}
      >
        高亮框
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().setCodeBlock({
          language: 'javascript',
        }).run()}
      >
        代码块
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().setSection().run()}
      >
        分区
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().setDivider().run()}
      >
        分割线
      </Button>
    </div>
  );
}
```

## 🎯 样式集成

```scss
// src/styles/editor.scss
.wechat-editor {
  .cyberpunk-editor {
    // 编辑器基础样式
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    
    // WeChat组件样式继承
    .image-container,
    .highlight-box,
    .code-block,
    .section,
    .divider {
      // 保持原有组件样式
      @apply mb-4;
    }
    
    // 选中状态样式
    .selected {
      outline: 2px solid #00d4ff;
      outline-offset: 2px;
      border-radius: 4px;
    }
    
    // 编辑状态样式
    .editing {
      background: rgba(0, 212, 255, 0.1);
      border: 1px dashed #00d4ff;
    }
  }
}
```

## 🔗 相关文档

- [06-内容转换机制](./06-content-conversion.md) - 内容转换实现
- [07-样式一致性保证](./07-style-consistency.md) - 样式一致性
- [04-API设计](./04-api-design.md) - 数据接口设计

---

**下一步**: 阅读 [04-API设计](./04-api-design.md) 了解数据接口实现
