# 07-样式一致性保证

## 📋 概述

本文档详细说明如何确保编辑器中的预览与最终微信公众号显示效果的一致性，这是用户体验的关键保障。

## 🎯 一致性目标

### 核心要求
- **100%视觉一致性**: 编辑器预览 = 微信公众号显示
- **样式完整性**: 赛博朋克主题完全保留
- **响应式兼容**: 适配不同屏幕尺寸
- **微信兼容性**: 符合微信编辑器限制

## 🏗 样式架构设计

### 三层样式系统

```
┌─────────────────────────────────────────────────────────────┐
│                    基础样式层 (Base Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  重置样式  │  字体定义  │  颜色变量  │  基础布局             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   组件样式层 (Component Layer)                │
├─────────────────────────────────────────────────────────────┤
│  WeChat组件  │  编辑器组件  │  预览组件  │  导出组件         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   主题样式层 (Theme Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  赛博朋克主题  │  动画效果  │  特效处理  │  响应式适配       │
└─────────────────────────────────────────────────────────────┘
```

## 🎨 样式变量系统

### 1. CSS 自定义属性定义

```scss
// styles/cyberpunk-variables.scss
:root {
  // 赛博朋克主色调
  --cyberpunk-primary: #00d4ff;      // 青色
  --cyberpunk-secondary: #ff6bb3;    // 粉色
  --cyberpunk-accent: #39ff14;       // 绿色
  
  // 背景色系
  --cyberpunk-bg-primary: #0a0a0a;
  --cyberpunk-bg-secondary: #1a1a2e;
  --cyberpunk-bg-tertiary: #16213e;
  
  // 文本色系
  --cyberpunk-text-primary: #ffffff;
  --cyberpunk-text-secondary: #b0b0b0;
  --cyberpunk-text-accent: #00d4ff;
  
  // 边框和阴影
  --cyberpunk-border: 1px solid var(--cyberpunk-primary);
  --cyberpunk-shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --cyberpunk-shadow-pink: 0 0 15px rgba(255, 107, 179, 0.2);
  
  // 渐变定义
  --cyberpunk-gradient-primary: linear-gradient(135deg, 
    var(--cyberpunk-primary) 0%, 
    var(--cyberpunk-secondary) 100%);
  --cyberpunk-gradient-bg: linear-gradient(135deg, 
    var(--cyberpunk-bg-secondary) 0%, 
    var(--cyberpunk-bg-tertiary) 100%);
  
  // 动画时长
  --cyberpunk-transition-fast: 0.2s ease;
  --cyberpunk-transition-normal: 0.3s ease;
  --cyberpunk-transition-slow: 0.5s ease;
  
  // 字体定义
  --cyberpunk-font-family: -apple-system, BlinkMacSystemFont, 
    "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", 
    "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
  --cyberpunk-font-mono: "SF Mono", Monaco, "Cascadia Code", 
    "Roboto Mono", Consolas, "Courier New", monospace;
}

// 微信编辑器兼容性调整
.wechat-export {
  // 微信不支持CSS变量，需要使用具体值
  --cyberpunk-primary: #00d4ff;
  --cyberpunk-secondary: #ff6bb3;
  // ... 其他变量的具体值
}
```

### 2. 样式混入 (Mixins)

```scss
// styles/cyberpunk-mixins.scss
@mixin cyberpunk-glow($color: var(--cyberpunk-primary), $intensity: 0.3) {
  box-shadow: 0 0 10px rgba($color, $intensity),
              0 0 20px rgba($color, $intensity * 0.5),
              0 0 30px rgba($color, $intensity * 0.3);
}

@mixin cyberpunk-text-glow($color: var(--cyberpunk-primary)) {
  text-shadow: 0 0 5px rgba($color, 0.5),
               0 0 10px rgba($color, 0.3),
               0 0 15px rgba($color, 0.2);
}

@mixin cyberpunk-border($color: var(--cyberpunk-primary)) {
  border: 1px solid $color;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, $color, transparent, $color);
    z-index: -1;
    border-radius: inherit;
  }
}

@mixin cyberpunk-animation($name, $duration: 2s) {
  animation: $name $duration ease-in-out infinite alternate;
}

// 响应式断点
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}
```

## 🔧 样式同步机制

### 1. 样式提取器

```typescript
// lib/style-sync/style-extractor.ts
export class StyleExtractor {
  private cssVariables: Map<string, string> = new Map();
  private computedStyles: Map<string, CSSStyleDeclaration> = new Map();
  
  extractFromElement(element: HTMLElement): ExtractedStyles {
    const computed = window.getComputedStyle(element);
    const inline = element.style;
    
    return {
      computed: this.serializeComputedStyle(computed),
      inline: this.serializeInlineStyle(inline),
      classes: Array.from(element.classList),
      cssVariables: this.extractCSSVariables(element),
    };
  }

  private extractCSSVariables(element: HTMLElement): Record<string, string> {
    const variables: Record<string, string> = {};
    const computed = window.getComputedStyle(element);
    
    // 提取所有CSS自定义属性
    for (let i = 0; i < computed.length; i++) {
      const property = computed[i];
      if (property.startsWith('--cyberpunk')) {
        variables[property] = computed.getPropertyValue(property);
      }
    }
    
    return variables;
  }

  serializeForExport(styles: ExtractedStyles): string {
    // 将样式转换为内联CSS，用于微信导出
    const cssRules: string[] = [];
    
    Object.entries(styles.computed).forEach(([property, value]) => {
      // 替换CSS变量为具体值
      const resolvedValue = this.resolveCSSVariable(value);
      cssRules.push(`${property}: ${resolvedValue}`);
    });
    
    return cssRules.join('; ');
  }

  private resolveCSSVariable(value: string): string {
    // 解析CSS变量引用
    return value.replace(/var\((--[\w-]+)(?:,\s*([^)]+))?\)/g, (match, varName, fallback) => {
      const resolvedValue = this.cssVariables.get(varName);
      return resolvedValue || fallback || match;
    });
  }
}
```

### 2. 样式同步服务

```typescript
// lib/style-sync/style-sync-service.ts
export class StyleSyncService {
  private extractor = new StyleExtractor();
  private observer: MutationObserver;
  
  constructor() {
    this.setupMutationObserver();
  }

  syncEditorToPreview(editorElement: HTMLElement, previewElement: HTMLElement): void {
    // 提取编辑器样式
    const editorStyles = this.extractor.extractFromElement(editorElement);
    
    // 应用到预览元素
    this.applyStylesToElement(previewElement, editorStyles);
    
    // 同步子元素
    this.syncChildElements(editorElement, previewElement);
  }

  private applyStylesToElement(element: HTMLElement, styles: ExtractedStyles): void {
    // 应用类名
    element.className = styles.classes.join(' ');
    
    // 应用内联样式
    Object.entries(styles.computed).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
    
    // 应用CSS变量
    Object.entries(styles.cssVariables).forEach(([variable, value]) => {
      element.style.setProperty(variable, value);
    });
  }

  private syncChildElements(source: HTMLElement, target: HTMLElement): void {
    const sourceChildren = Array.from(source.children) as HTMLElement[];
    const targetChildren = Array.from(target.children) as HTMLElement[];
    
    sourceChildren.forEach((sourceChild, index) => {
      const targetChild = targetChildren[index];
      if (targetChild) {
        const childStyles = this.extractor.extractFromElement(sourceChild);
        this.applyStylesToElement(targetChild, childStyles);
        
        // 递归同步子元素
        this.syncChildElements(sourceChild, targetChild);
      }
    });
  }

  private setupMutationObserver(): void {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
          // 样式变化时触发同步
          this.handleStyleChange(mutation.target as HTMLElement);
        }
      });
    });
  }

  startObserving(element: HTMLElement): void {
    this.observer.observe(element, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      subtree: true,
    });
  }

  stopObserving(): void {
    this.observer.disconnect();
  }
}
```

## 🎭 预览模式实现

### 1. 实时预览组件

```typescript
// components/editor/preview/RealTimePreview.tsx
'use client';

import { useEffect, useRef, useState } from 'react';
import { useEditor } from '@tiptap/react';
import { StyleSyncService } from '@/lib/style-sync/style-sync-service';
import { TipTapRenderer } from '@/lib/renderers/tiptap-renderer';

interface RealTimePreviewProps {
  content: any; // TipTap JSON
  className?: string;
}

export function RealTimePreview({ content, className }: RealTimePreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const [syncService] = useState(() => new StyleSyncService());
  const [renderer] = useState(() => new TipTapRenderer());
  
  useEffect(() => {
    if (previewRef.current && content) {
      // 渲染TipTap内容为React组件
      const renderedContent = renderer.renderToReact(content);
      
      // 应用赛博朋克样式
      previewRef.current.innerHTML = '';
      previewRef.current.appendChild(renderedContent);
      
      // 应用样式类
      previewRef.current.classList.add('cyberpunk-preview', 'article-container');
    }
  }, [content, renderer]);

  return (
    <div className={`preview-container ${className || ''}`}>
      <div 
        ref={previewRef}
        className="cyberpunk-preview-content"
        style={{
          fontFamily: 'var(--cyberpunk-font-family)',
          lineHeight: '1.8',
          color: 'var(--cyberpunk-text-primary)',
        }}
      />
    </div>
  );
}
```

### 2. 样式隔离容器

```scss
// styles/preview-isolation.scss
.cyberpunk-preview {
  // 创建独立的样式上下文
  isolation: isolate;
  contain: layout style;
  
  // 重置所有可能的外部样式影响
  all: initial;
  
  // 重新应用基础样式
  font-family: var(--cyberpunk-font-family);
  font-size: 16px;
  line-height: 1.8;
  color: var(--cyberpunk-text-primary);
  
  // 确保容器样式
  .article-container,
  .phone-frame,
  .article-outer-container {
    // 导入现有的微信文章样式
    @import '../blogStyles.scss';
    @import '../wechat-article.scss';
    
    // 确保样式优先级
    && {
      // 使用双重类名选择器提高优先级
      background-color: #ffffff;
      max-width: 100%;
      padding: 20px 16px;
      margin: 0 auto;
      
      // 标题样式
      h2 {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin: 30px 0 15px 0;
        padding: 12px 16px 12px 16px;
        border-bottom: 2px solid var(--cyberpunk-primary);
        border-left: 4px solid var(--cyberpunk-secondary);
        background: linear-gradient(90deg, 
          rgba(255, 107, 179, 0.08) 0%, 
          transparent 50%);
        position: relative;
        border-radius: 0 8px 8px 0;
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.1);
      }
      
      // 强调文本样式
      strong {
        color: var(--cyberpunk-secondary);
        font-weight: bold;
        text-shadow: 0 0 8px rgba(255, 107, 179, 0.3);
        position: relative;
      }
      
      // 链接样式
      a {
        color: var(--cyberpunk-primary);
        text-decoration: none;
        word-break: break-all;
        text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        
        &:hover {
          color: var(--cyberpunk-secondary);
          text-shadow: 0 0 10px rgba(255, 107, 179, 0.5);
        }
      }
    }
  }
}
```

## 📱 响应式一致性

### 1. 断点同步

```typescript
// lib/style-sync/responsive-sync.ts
export class ResponsiveSync {
  private breakpoints = {
    mobile: '(max-width: 768px)',
    tablet: '(min-width: 769px) and (max-width: 1024px)',
    desktop: '(min-width: 1025px)',
  };
  
  private mediaQueries: Map<string, MediaQueryList> = new Map();
  
  constructor() {
    this.setupMediaQueries();
  }

  private setupMediaQueries(): void {
    Object.entries(this.breakpoints).forEach(([name, query]) => {
      const mq = window.matchMedia(query);
      this.mediaQueries.set(name, mq);
      
      mq.addEventListener('change', (e) => {
        if (e.matches) {
          this.handleBreakpointChange(name);
        }
      });
    });
  }

  private handleBreakpointChange(breakpoint: string): void {
    // 触发样式重新计算和同步
    document.documentElement.setAttribute('data-breakpoint', breakpoint);
    
    // 通知所有预览组件更新
    window.dispatchEvent(new CustomEvent('breakpoint-change', {
      detail: { breakpoint }
    }));
  }

  getCurrentBreakpoint(): string {
    for (const [name, mq] of this.mediaQueries) {
      if (mq.matches) {
        return name;
      }
    }
    return 'desktop';
  }
}
```

### 2. 移动端优化

```scss
// styles/mobile-optimization.scss
.cyberpunk-preview {
  @include mobile {
    // 移动端特定优化
    .article-container {
      padding: 16px 12px;
      
      .article-title {
        font-size: 20px;
        padding: 16px 8px;
      }
      
      h2 {
        font-size: 18px;
        padding: 10px 12px 10px 12px;
      }
      
      h3 {
        font-size: 16px;
        padding: 6px 12px 6px 12px;
      }
      
      // 减少动画效果以提升性能
      * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
      }
    }
  }
  
  @include tablet {
    // 平板端优化
    .article-container {
      padding: 18px 14px;
      max-width: 90%;
    }
  }
  
  @include desktop {
    // 桌面端优化
    .article-container {
      padding: 20px 16px;
      max-width: 800px;
    }
  }
}
```

## 🔍 样式验证系统

### 1. 样式一致性检查器

```typescript
// lib/style-sync/consistency-checker.ts
export class ConsistencyChecker {
  checkStyleConsistency(
    editorElement: HTMLElement, 
    previewElement: HTMLElement
  ): ConsistencyReport {
    const editorStyles = this.extractComputedStyles(editorElement);
    const previewStyles = this.extractComputedStyles(previewElement);
    
    const differences = this.compareStyles(editorStyles, previewStyles);
    
    return {
      isConsistent: differences.length === 0,
      differences,
      score: this.calculateConsistencyScore(differences),
      recommendations: this.generateRecommendations(differences),
    };
  }

  private compareStyles(
    styles1: ComputedStyleMap, 
    styles2: ComputedStyleMap
  ): StyleDifference[] {
    const differences: StyleDifference[] = [];
    const criticalProperties = [
      'color', 'background-color', 'font-size', 'font-weight',
      'margin', 'padding', 'border', 'box-shadow', 'text-shadow'
    ];
    
    criticalProperties.forEach(property => {
      const value1 = styles1.get(property);
      const value2 = styles2.get(property);
      
      if (!this.areStyleValuesEqual(value1, value2)) {
        differences.push({
          property,
          editorValue: value1,
          previewValue: value2,
          severity: this.getSeverity(property),
        });
      }
    });
    
    return differences;
  }

  private areStyleValuesEqual(value1: any, value2: any): boolean {
    // 处理颜色值的不同表示形式
    if (this.isColorProperty(value1) && this.isColorProperty(value2)) {
      return this.normalizeColor(value1) === this.normalizeColor(value2);
    }
    
    // 处理数值的精度差异
    if (this.isNumericProperty(value1) && this.isNumericProperty(value2)) {
      return Math.abs(parseFloat(value1) - parseFloat(value2)) < 0.1;
    }
    
    return value1 === value2;
  }
}
```

## 🔗 相关文档

- [06-内容转换机制](./06-content-conversion.md) - 内容转换实现
- [08-微信编辑器集成](./08-wechat-integration.md) - 最终导出样式处理
- [03-编辑器实现](./03-editor-implementation.md) - 编辑器样式集成
- [12-流程图集合](./12-workflow-diagrams.md) - 样式处理流程图

---

**下一步**: 阅读 [08-微信编辑器集成](./08-wechat-integration.md) 了解微信编辑器集成的技术细节
