# 12-流程图集合

## 📋 概述

本文档使用 Mermaid 图表展示微信公众号文章工作流程优化系统的关键流程，包括整体工作流程、数据流转、系统架构和组件转换流程。

## 🔄 整体工作流程图

### 完整工作流程

```mermaid
flowchart TD
    A[内容创作<br/>Gemini/其他工具] --> B[复制内容<br/>HTML/Markdown]
    B --> C[粘贴到管理界面<br/>内容导入区域]
    C --> D[智能格式识别<br/>HTML/Markdown解析]
    D --> E[组件映射转换<br/>转换为WeChat组件]
    E --> F[TipTap编辑器<br/>可视化编辑]
    F --> G[实时预览<br/>赛博朋克样式]
    G --> H{编辑完成?}
    H -->|否| F
    H -->|是| I[保存到数据库<br/>TipTap JSON格式]
    I --> J[生成预览HTML<br/>应用赛博样式]
    J --> K[一键导出<br/>微信编辑器格式]
    K --> L[复制到微信公众号<br/>保持样式一致]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#e8f5e8
    style K fill:#fff3e0
    style L fill:#ffebee
```

## 📊 数据流转图

### 内容转换数据流

```mermaid
flowchart LR
    subgraph "输入格式"
        A1[HTML内容]
        A2[Markdown内容]
        A3[纯文本内容]
    end
    
    subgraph "解析层"
        B1[HTML解析器]
        B2[Markdown解析器]
        B3[文本解析器]
    end
    
    subgraph "转换层"
        C1[组件识别器]
        C2[样式映射器]
        C3[结构转换器]
    end
    
    subgraph "TipTap层"
        D1[TipTap JSON]
        D2[编辑器渲染]
        D3[组件扩展]
    end
    
    subgraph "输出格式"
        E1[React组件]
        E2[预览HTML]
        E3[微信HTML]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    
    D1 --> D2
    D2 --> D3
    
    D1 --> E1
    D1 --> E2
    D1 --> E3
    
    style D1 fill:#e3f2fd
    style E3 fill:#fff3e0
```

## 🏗 系统架构图

### 技术架构层次

```mermaid
graph TB
    subgraph "用户界面层"
        UI1[管理界面<br/>文章列表/编辑]
        UI2[编辑器界面<br/>TipTap编辑器]
        UI3[预览界面<br/>实时预览]
        UI4[导出界面<br/>微信格式]
    end
    
    subgraph "业务逻辑层"
        BL1[内容转换服务<br/>格式转换]
        BL2[组件映射服务<br/>WeChat组件]
        BL3[样式处理服务<br/>赛博样式]
        BL4[导出服务<br/>微信兼容]
    end
    
    subgraph "数据访问层"
        DA1[Prisma ORM<br/>类型安全]
        DA2[PostgreSQL<br/>主数据库]
        DA3[文件系统<br/>图片存储]
        DA4[Redis缓存<br/>性能优化]
    end
    
    subgraph "基础设施层"
        IF1[Next.js<br/>全栈框架]
        IF2[TypeScript<br/>类型系统]
        IF3[Tailwind CSS<br/>样式框架]
        IF4[Docker<br/>容器化]
    end
    
    UI1 --> BL1
    UI2 --> BL2
    UI3 --> BL3
    UI4 --> BL4
    
    BL1 --> DA1
    BL2 --> DA1
    BL3 --> DA2
    BL4 --> DA3
    
    DA1 --> IF1
    DA2 --> IF1
    DA3 --> IF2
    DA4 --> IF3
    
    style BL2 fill:#e8f5e8
    style DA1 fill:#e3f2fd
```

## 🧩 组件转换流程图

### WeChat组件映射流程

```mermaid
flowchart TD
    A[输入内容解析] --> B{识别内容类型}
    
    B -->|强调文本| C1[检测strong/b标签]
    B -->|代码块| C2[检测pre/code标签]
    B -->|引用块| C3[检测blockquote标签]
    B -->|图片| C4[检测img标签]
    B -->|表格| C5[检测table标签]
    
    C1 --> D1{上下文分析}
    D1 -->|独立强调| E1[转换为HighlightBox]
    D1 -->|内联强调| E2[保持strong样式]
    
    C2 --> D2{代码类型检测}
    D2 -->|多行代码| E3[转换为CodeBlock]
    D2 -->|内联代码| E4[保持code样式]
    
    C3 --> E5[转换为HighlightBox<br/>type=info]
    
    C4 --> D4{图片上下文}
    D4 -->|独立图片| E6[转换为ImageContainer]
    D4 -->|内联图片| E7[保持img标签]
    
    C5 --> E8[应用赛博样式<br/>保持table结构]
    
    E1 --> F[生成TipTap扩展节点]
    E3 --> F
    E5 --> F
    E6 --> F
    
    E2 --> G[生成标准HTML节点]
    E4 --> G
    E7 --> G
    E8 --> G
    
    F --> H[TipTap JSON输出]
    G --> H
    
    style E1 fill:#e8f5e8
    style E3 fill:#fff3e0
    style E5 fill:#e1f5fe
    style E6 fill:#f3e5f5
```

## 🎨 样式处理流程图

### 赛博朋克样式应用流程

```mermaid
flowchart LR
    subgraph "样式输入"
        A1[原始HTML样式]
        A2[Markdown样式]
        A3[组件默认样式]
    end
    
    subgraph "样式处理"
        B1[样式解析器<br/>提取CSS规则]
        B2[赛博样式映射<br/>cyan/pink主题]
        B3[响应式处理<br/>移动端优化]
        B4[兼容性处理<br/>微信编辑器]
    end
    
    subgraph "样式输出"
        C1[编辑器样式<br/>实时预览]
        C2[预览样式<br/>完整效果]
        C3[导出样式<br/>内联CSS]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B4 --> C1
    B4 --> C2
    B4 --> C3
    
    style B2 fill:#e8f5e8
    style C3 fill:#fff3e0
```

## 🔄 编辑器交互流程图

### TipTap编辑器操作流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant E as TipTap编辑器
    participant C as 组件扩展
    participant S as 状态管理
    participant D as 数据库
    
    U->>E: 输入内容
    E->>C: 触发组件识别
    C->>C: 解析内容类型
    C->>E: 渲染对应组件
    E->>S: 更新编辑器状态
    
    U->>E: 插入WeChat组件
    E->>C: 调用组件扩展
    C->>C: 创建组件节点
    C->>E: 插入到编辑器
    E->>S: 保存到状态
    
    U->>E: 修改组件属性
    E->>C: 更新组件配置
    C->>C: 验证属性值
    C->>E: 重新渲染组件
    E->>S: 同步状态变更
    
    S->>D: 自动保存内容
    D->>S: 返回保存结果
    S->>E: 更新保存状态
    E->>U: 显示保存提示
```

## 📤 导出流程图

### 微信编辑器导出流程

```mermaid
flowchart TD
    A[用户点击导出] --> B[获取TipTap JSON]
    B --> C[转换为React组件]
    C --> D[应用赛博样式]
    D --> E[生成HTML结构]
    E --> F[样式内联处理]
    F --> G[图片路径转换]
    G --> H[微信兼容性优化]
    H --> I[生成最终HTML]
    I --> J[复制到剪贴板]
    J --> K[用户粘贴到微信]
    
    subgraph "样式处理"
        F1[提取CSS规则]
        F2[内联到HTML标签]
        F3[移除不兼容样式]
        F4[优化文件大小]
    end
    
    F --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
    F4 --> G
    
    subgraph "兼容性处理"
        H1[移除不支持的CSS]
        H2[转换颜色格式]
        H3[简化动画效果]
        H4[优化字体设置]
    end
    
    H --> H1
    H1 --> H2
    H2 --> H3
    H3 --> H4
    H4 --> I
    
    style F fill:#e8f5e8
    style H fill:#fff3e0
    style J fill:#ffebee
```

## 🔧 错误处理流程图

### 系统错误处理机制

```mermaid
flowchart TD
    A[操作执行] --> B{是否成功?}
    B -->|成功| C[返回结果]
    B -->|失败| D[错误分类]
    
    D --> E{错误类型}
    E -->|网络错误| F1[重试机制<br/>最多3次]
    E -->|数据错误| F2[数据验证<br/>错误提示]
    E -->|系统错误| F3[日志记录<br/>降级处理]
    E -->|用户错误| F4[友好提示<br/>操作指导]
    
    F1 --> G1{重试成功?}
    G1 -->|是| C
    G1 -->|否| H[记录错误日志]
    
    F2 --> I[显示错误信息]
    F3 --> J[启用备用方案]
    F4 --> K[用户操作指导]
    
    H --> L[通知管理员]
    I --> M[用户重新操作]
    J --> N[降级功能]
    K --> M
    
    style F3 fill:#ffebee
    style L fill:#fff3e0
```

## 🔗 相关文档

- [01-核心架构设计](./01-core-architecture.md) - 详细架构说明
- [06-内容转换机制](./06-content-conversion.md) - 转换机制实现
- [07-样式一致性保证](./07-style-consistency.md) - 样式处理详情
- [08-微信编辑器集成](./08-wechat-integration.md) - 导出机制实现

---

**说明**: 这些流程图展示了系统的核心工作流程，可以帮助理解各个模块之间的关系和数据流转过程。
