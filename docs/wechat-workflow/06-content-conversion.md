# 06-内容转换机制（核心）

## 📋 概述

本文档详细解释如何将外部 HTML/Markdown 内容转换为项目的赛博朋克风格富文本，这是整个工作流程优化的核心机制。

## 🔄 转换流程概览

### 三阶段转换架构

```
输入内容 → 解析识别 → 组件映射 → TipTap JSON → 赛博样式渲染
    ↓           ↓           ↓           ↓           ↓
HTML/MD    格式解析    WeChat组件   编辑器格式   最终显示
```

## 🧠 智能识别算法

### 1. 内容类型识别

```typescript
// lib/converters/content-analyzer.ts
export interface ContentAnalysisResult {
  type: 'html' | 'markdown' | 'plain';
  confidence: number;
  structure: ContentStructure;
  components: ComponentHint[];
}

export class ContentAnalyzer {
  analyze(content: string): ContentAnalysisResult {
    // 1. 格式检测
    const htmlScore = this.detectHTML(content);
    const markdownScore = this.detectMarkdown(content);
    
    // 2. 结构分析
    const structure = this.analyzeStructure(content);
    
    // 3. 组件提示
    const components = this.identifyComponents(content, structure);
    
    return {
      type: htmlScore > markdownScore ? 'html' : 'markdown',
      confidence: Math.max(htmlScore, markdownScore),
      structure,
      components,
    };
  }

  private detectHTML(content: string): number {
    const htmlPatterns = [
      /<[^>]+>/g,                    // HTML标签
      /&[a-zA-Z]+;/g,               // HTML实体
      /<(div|p|span|strong|em)/gi,  // 常见HTML标签
    ];
    
    let score = 0;
    htmlPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) score += matches.length * 0.1;
    });
    
    return Math.min(score, 1);
  }

  private detectMarkdown(content: string): number {
    const markdownPatterns = [
      /^#{1,6}\s/gm,                // 标题
      /\*\*[^*]+\*\*/g,            // 粗体
      /\*[^*]+\*/g,                // 斜体
      /```[\s\S]*?```/g,           // 代码块
      /`[^`]+`/g,                  // 内联代码
      /^\s*[-*+]\s/gm,             // 列表
    ];
    
    let score = 0;
    markdownPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) score += matches.length * 0.15;
    });
    
    return Math.min(score, 1);
  }
}
```

### 2. 组件识别规则

```typescript
// lib/converters/component-detector.ts
export interface ComponentRule {
  name: string;
  pattern: RegExp | ((element: Element) => boolean);
  priority: number;
  converter: ComponentConverter;
}

export const COMPONENT_RULES: ComponentRule[] = [
  // 高亮框识别规则
  {
    name: 'highlightBox',
    pattern: (element: Element) => {
      // 独立的强调文本块
      if (element.tagName === 'BLOCKQUOTE') return true;
      
      // 包含特定关键词的段落
      if (element.tagName === 'P') {
        const text = element.textContent?.toLowerCase() || '';
        const keywords = ['注意', '重要', '提示', '警告', '错误'];
        return keywords.some(keyword => text.includes(keyword));
      }
      
      // 独立的强调标签
      if (element.tagName === 'STRONG' || element.tagName === 'B') {
        const parent = element.parentElement;
        return parent?.children.length === 1; // 唯一子元素
      }
      
      return false;
    },
    priority: 8,
    converter: new HighlightBoxConverter(),
  },
  
  // 代码块识别规则
  {
    name: 'codeBlock',
    pattern: (element: Element) => {
      if (element.tagName === 'PRE') return true;
      
      // 多行代码（超过2行的code标签）
      if (element.tagName === 'CODE') {
        const text = element.textContent || '';
        return text.split('\n').length > 2;
      }
      
      return false;
    },
    priority: 9,
    converter: new CodeBlockConverter(),
  },
  
  // 图片容器识别规则
  {
    name: 'imageContainer',
    pattern: (element: Element) => {
      if (element.tagName === 'IMG') {
        // 检查是否有标题或说明
        const nextSibling = element.nextElementSibling;
        const prevSibling = element.previousElementSibling;
        
        // 有图片说明的情况
        if (nextSibling?.tagName === 'P' && 
            nextSibling.textContent?.length < 100) {
          return true;
        }
        
        // 独立图片（父元素只有这一个子元素）
        const parent = element.parentElement;
        return parent?.children.length === 1;
      }
      
      return false;
    },
    priority: 7,
    converter: new ImageContainerConverter(),
  },
  
  // 分区识别规则
  {
    name: 'section',
    pattern: (element: Element) => {
      // 包含标题的div
      if (element.tagName === 'DIV') {
        const hasHeading = element.querySelector('h1, h2, h3, h4, h5, h6');
        const hasContent = element.children.length > 1;
        return hasHeading && hasContent;
      }
      
      // 语义化标签
      return ['SECTION', 'ARTICLE', 'ASIDE'].includes(element.tagName);
    },
    priority: 5,
    converter: new SectionConverter(),
  },
];
```

## 🎯 组件映射转换器

### 1. HighlightBox 转换器

```typescript
// lib/converters/highlight-box-converter.ts
export class HighlightBoxConverter implements ComponentConverter {
  convert(element: Element, context: ConversionContext): TipTapNode {
    const type = this.detectHighlightType(element);
    const title = this.extractTitle(element);
    const content = this.extractContent(element);
    
    return {
      type: 'highlightBox',
      attrs: {
        type,
        title,
      },
      content: content.map(node => this.convertChildNode(node, context)),
    };
  }

  private detectHighlightType(element: Element): HighlightType {
    const text = element.textContent?.toLowerCase() || '';
    const className = element.className?.toLowerCase() || '';
    
    // 基于关键词判断类型
    if (text.includes('错误') || text.includes('error') || 
        className.includes('error') || className.includes('danger')) {
      return 'error';
    }
    
    if (text.includes('警告') || text.includes('warning') || 
        className.includes('warning')) {
      return 'warning';
    }
    
    if (text.includes('成功') || text.includes('success') || 
        className.includes('success')) {
      return 'success';
    }
    
    return 'info'; // 默认类型
  }

  private extractTitle(element: Element): string {
    // 尝试从第一个强调元素提取标题
    const firstStrong = element.querySelector('strong, b');
    if (firstStrong && firstStrong.textContent) {
      const text = firstStrong.textContent.trim();
      if (text.length < 50) { // 标题不应太长
        return text;
      }
    }
    
    // 尝试从特定模式提取标题
    const text = element.textContent || '';
    const titleMatch = text.match(/^(注意|重要|提示|警告|错误)[：:]\s*/);
    if (titleMatch) {
      return titleMatch[1];
    }
    
    return '';
  }
}
```

### 2. CodeBlock 转换器

```typescript
// lib/converters/code-block-converter.ts
export class CodeBlockConverter implements ComponentConverter {
  convert(element: Element, context: ConversionContext): TipTapNode {
    const language = this.detectLanguage(element);
    const title = this.extractTitle(element);
    const code = this.extractCode(element);
    
    return {
      type: 'codeBlock',
      attrs: {
        language,
        title,
      },
      content: [
        {
          type: 'text',
          text: code,
        },
      ],
    };
  }

  private detectLanguage(element: Element): string {
    // 从class属性检测语言
    const className = element.className || '';
    const langMatch = className.match(/language-(\w+)|lang-(\w+)/);
    if (langMatch) {
      return langMatch[1] || langMatch[2];
    }
    
    // 从代码内容推断语言
    const code = this.extractCode(element);
    return this.inferLanguageFromCode(code);
  }

  private inferLanguageFromCode(code: string): string {
    const patterns = {
      javascript: [/function\s+\w+/, /const\s+\w+\s*=/, /=>\s*{/, /console\.log/],
      typescript: [/interface\s+\w+/, /type\s+\w+\s*=/, /:\s*string/, /:\s*number/],
      python: [/def\s+\w+/, /import\s+\w+/, /print\(/, /if\s+__name__/],
      css: [/\.\w+\s*{/, /#\w+\s*{/, /@media/, /background:/],
      html: [/<\w+/, /<\/\w+>/, /<!DOCTYPE/, /<html/],
      bash: [/^\$\s/, /npm\s+/, /git\s+/, /sudo\s+/],
    };
    
    for (const [lang, langPatterns] of Object.entries(patterns)) {
      if (langPatterns.some(pattern => pattern.test(code))) {
        return lang;
      }
    }
    
    return 'text';
  }
}
```

### 3. ImageContainer 转换器

```typescript
// lib/converters/image-container-converter.ts
export class ImageContainerConverter implements ComponentConverter {
  convert(element: Element, context: ConversionContext): TipTapNode {
    const src = this.processSrc(element.getAttribute('src') || '');
    const alt = element.getAttribute('alt') || '';
    const caption = this.extractCaption(element);
    const dimensions = this.extractDimensions(element);
    
    return {
      type: 'imageContainer',
      attrs: {
        src,
        alt,
        caption,
        width: dimensions.width,
        height: dimensions.height,
      },
    };
  }

  private processSrc(src: string): string {
    // 处理相对路径
    if (src.startsWith('./') || src.startsWith('../')) {
      // 转换为绝对路径或上传到服务器
      return this.uploadOrConvertPath(src);
    }
    
    // 处理外部链接
    if (src.startsWith('http')) {
      // 可选：下载到本地服务器
      return this.downloadAndStore(src);
    }
    
    return src;
  }

  private extractCaption(element: Element): string {
    // 检查下一个兄弟元素是否为图片说明
    const nextSibling = element.nextElementSibling;
    if (nextSibling?.tagName === 'P') {
      const text = nextSibling.textContent?.trim() || '';
      if (text.length < 100 && text.length > 0) {
        return text;
      }
    }
    
    // 检查父元素的figure/figcaption结构
    const parent = element.parentElement;
    if (parent?.tagName === 'FIGURE') {
      const figcaption = parent.querySelector('figcaption');
      if (figcaption) {
        return figcaption.textContent?.trim() || '';
      }
    }
    
    return '';
  }
}
```

## 🎨 样式映射规则

### 1. 赛博朋克样式映射

```typescript
// lib/converters/style-mapper.ts
export interface StyleMapping {
  selector: string;
  cyberpunkStyle: CSSProperties;
  priority: number;
}

export const CYBERPUNK_STYLE_MAPPINGS: StyleMapping[] = [
  // 标题样式映射
  {
    selector: 'h1, h2, h3, h4, h5, h6',
    cyberpunkStyle: {
      background: 'linear-gradient(90deg, rgba(255, 107, 179, 0.08) 0%, transparent 50%)',
      borderLeft: '4px solid #ff6bb3',
      borderBottom: '2px solid #00d4ff',
      padding: '12px 16px',
      borderRadius: '0 8px 8px 0',
      boxShadow: '0 0 15px rgba(0, 212, 255, 0.1)',
      color: '#333',
      position: 'relative',
    },
    priority: 8,
  },
  
  // 强调文本样式
  {
    selector: 'strong, b',
    cyberpunkStyle: {
      color: '#ff6bb3',
      fontWeight: 'bold',
      textShadow: '0 0 8px rgba(255, 107, 179, 0.3)',
      position: 'relative',
    },
    priority: 7,
  },
  
  // 链接样式
  {
    selector: 'a',
    cyberpunkStyle: {
      color: '#00d4ff',
      textDecoration: 'none',
      textShadow: '0 0 5px rgba(0, 212, 255, 0.3)',
      transition: 'all 0.3s ease',
      position: 'relative',
    },
    priority: 6,
  },
  
  // 代码样式
  {
    selector: 'code',
    cyberpunkStyle: {
      fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
      backgroundColor: '#f5f5f5',
      padding: '2px 4px',
      borderRadius: '3px',
      fontSize: '14px',
      color: '#ff6bb3',
    },
    priority: 9,
  },
  
  // 代码块样式
  {
    selector: 'pre',
    cyberpunkStyle: {
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
      border: '1px solid #00d4ff',
      borderRadius: '8px',
      padding: '16px',
      margin: '16px 0',
      overflowX: 'auto',
      fontSize: '14px',
      lineHeight: '1.4',
      position: 'relative',
      boxShadow: '0 0 20px rgba(0, 212, 255, 0.1)',
    },
    priority: 10,
  },
];

export class StyleMapper {
  applyMappings(element: Element): CSSProperties {
    const applicableMappings = CYBERPUNK_STYLE_MAPPINGS
      .filter(mapping => element.matches(mapping.selector))
      .sort((a, b) => b.priority - a.priority);
    
    let combinedStyle: CSSProperties = {};
    
    applicableMappings.forEach(mapping => {
      combinedStyle = { ...combinedStyle, ...mapping.cyberpunkStyle };
    });
    
    return combinedStyle;
  }
}
```

### 2. 动态样式增强

```typescript
// lib/converters/style-enhancer.ts
export class StyleEnhancer {
  enhanceWithCyberpunkEffects(styles: CSSProperties): CSSProperties {
    return {
      ...styles,
      // 添加发光效果
      filter: this.addGlowEffect(styles),
      // 添加动画效果
      animation: this.addAnimationEffect(styles),
      // 添加渐变背景
      background: this.enhanceBackground(styles.background),
    };
  }

  private addGlowEffect(styles: CSSProperties): string {
    const baseFilter = styles.filter || '';
    const glowEffects = [
      'drop-shadow(0 0 10px rgba(0, 212, 255, 0.3))',
      'drop-shadow(0 0 20px rgba(255, 107, 179, 0.2))',
    ];
    
    return [baseFilter, ...glowEffects].filter(Boolean).join(' ');
  }

  private addAnimationEffect(styles: CSSProperties): string {
    // 根据元素类型添加适当的动画
    if (styles.borderLeft?.includes('#ff6bb3')) {
      return 'cyberpunk-pulse 2s ease-in-out infinite alternate';
    }
    
    if (styles.textShadow?.includes('cyan')) {
      return 'cyberpunk-glow 3s ease-in-out infinite';
    }
    
    return styles.animation || '';
  }
}
```

## 🔄 转换流程实现

### 主转换器

```typescript
// lib/converters/main-converter.ts
export class MainContentConverter {
  private analyzer = new ContentAnalyzer();
  private componentDetector = new ComponentDetector();
  private styleMapper = new StyleMapper();
  
  async convert(content: string): Promise<TipTapDocument> {
    // 1. 分析内容类型
    const analysis = this.analyzer.analyze(content);
    
    // 2. 解析为DOM结构
    const dom = this.parseContent(content, analysis.type);
    
    // 3. 识别和转换组件
    const tiptapNodes = await this.convertNodes(dom.body.children);
    
    // 4. 生成TipTap文档
    return {
      type: 'doc',
      content: tiptapNodes,
    };
  }

  private async convertNodes(elements: HTMLCollection): Promise<TipTapNode[]> {
    const nodes: TipTapNode[] = [];
    
    for (const element of Array.from(elements)) {
      const componentRule = this.componentDetector.detect(element);
      
      if (componentRule) {
        // 转换为WeChat组件
        const componentNode = await componentRule.converter.convert(element, {
          styleMapper: this.styleMapper,
        });
        nodes.push(componentNode);
      } else {
        // 转换为标准HTML节点
        const htmlNode = await this.convertStandardElement(element);
        nodes.push(htmlNode);
      }
    }
    
    return nodes;
  }

  private async convertStandardElement(element: Element): Promise<TipTapNode> {
    const tagName = element.tagName.toLowerCase();
    const styles = this.styleMapper.applyMappings(element);
    
    // 递归转换子元素
    const content = await this.convertNodes(element.children);
    
    return {
      type: this.mapHtmlTagToTipTap(tagName),
      attrs: {
        ...this.extractAttributes(element),
        style: styles,
      },
      content: content.length > 0 ? content : this.extractTextContent(element),
    };
  }
}
```

## 🔗 相关文档

- [07-样式一致性保证](./07-style-consistency.md) - 样式一致性实现
- [08-微信编辑器集成](./08-wechat-integration.md) - 最终导出机制
- [03-编辑器实现](./03-editor-implementation.md) - TipTap编辑器集成
- [12-流程图集合](./12-workflow-diagrams.md) - 转换流程可视化

---

**下一步**: 阅读 [07-样式一致性保证](./07-style-consistency.md) 了解样式一致性实现细节
