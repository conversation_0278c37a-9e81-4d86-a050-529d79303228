# 04-API设计

## 📋 概述

本文档定义微信公众号文章工作流程优化系统的RESTful API接口设计，包括文章管理、内容转换和文件处理等核心功能。

## 🎯 API设计原则

- **RESTful规范**: 遵循REST API设计最佳实践
- **类型安全**: 使用TypeScript提供完整的类型定义
- **错误处理**: 统一的错误响应格式
- **性能优化**: 支持分页、缓存和压缩

## 📊 数据模型

### 核心类型定义

```typescript
// src/types/api.ts
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
```

### 文章相关类型

```typescript
// src/types/article.ts
export interface Article {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  contentJson: string;
  status: 'DRAFT' | 'PUBLISHED';
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateArticleRequest {
  slug: string;
  title: string;
  excerpt?: string;
  content: any; // TipTap JSON
  status?: 'DRAFT' | 'PUBLISHED';
}

export interface UpdateArticleRequest {
  slug?: string;
  title?: string;
  excerpt?: string;
  content?: any;
  status?: 'DRAFT' | 'PUBLISHED';
}

export interface ConvertContentRequest {
  content: string;
  type: 'html' | 'markdown';
  options?: {
    preserveFormatting?: boolean;
    autoDetectComponents?: boolean;
  };
}

export interface ConvertContentResponse {
  tiptapJson: any;
  suggestions: string[];
  componentUsage: {
    imageContainer: number;
    highlightBox: number;
    codeBlock: number;
    section: number;
    divider: number;
  };
}
```

## 🔗 API端点设计

### 1. 文章管理API

#### 获取文章列表
```typescript
// GET /api/articles
interface GetArticlesParams extends PaginationParams {
  status?: 'DRAFT' | 'PUBLISHED' | 'ALL';
  search?: string;
}

// Response
interface GetArticlesResponse extends PaginatedResponse<Article> {}
```

#### 获取单篇文章
```typescript
// GET /api/articles/[id]
// Response
interface GetArticleResponse extends ApiResponse<Article> {}
```

#### 创建文章
```typescript
// POST /api/articles
// Request Body: CreateArticleRequest
// Response
interface CreateArticleResponse extends ApiResponse<Article> {}
```

#### 更新文章
```typescript
// PUT /api/articles/[id]
// Request Body: UpdateArticleRequest
// Response
interface UpdateArticleResponse extends ApiResponse<Article> {}
```

#### 删除文章
```typescript
// DELETE /api/articles/[id]
// Response
interface DeleteArticleResponse extends ApiResponse<{ deleted: boolean }> {}
```

### 2. 内容转换API

#### 转换外部内容
```typescript
// POST /api/convert/content
// Request Body: ConvertContentRequest
// Response
interface ConvertResponse extends ApiResponse<ConvertContentResponse> {}
```

#### 导出微信格式
```typescript
// POST /api/export/wechat
interface ExportWeChatRequest {
  articleId: string;
  options?: {
    inlineStyles?: boolean;
    optimizeImages?: boolean;
  };
}

interface ExportWeChatResponse extends ApiResponse<{
  html: string;
  css: string;
  images: string[];
}> {}
```

### 3. 文件管理API

#### 上传图片
```typescript
// POST /api/upload/image
// Content-Type: multipart/form-data
interface UploadImageResponse extends ApiResponse<{
  url: string;
  filename: string;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
}> {}
```

## 🛠 API实现示例

### 1. 文章管理实现

```typescript
// src/app/api/articles/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { articleService } from '@/lib/services/article-service';
import { ApiResponse, PaginationParams } from '@/types/api';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params: PaginationParams & { status?: string; search?: string } = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
      status: searchParams.get('status') || 'ALL',
      search: searchParams.get('search') || '',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    };

    const result = await articleService.getArticles(params);
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: '获取文章列表失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const article = await articleService.createArticle(body);
    
    const response: ApiResponse<typeof article> = {
      success: true,
      data: article,
      message: '文章创建成功',
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: '创建文章失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}
```

### 2. 内容转换实现

```typescript
// src/app/api/convert/content/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { ContentConverter } from '@/lib/converters/content-converter';
import { ApiResponse, ConvertContentRequest, ConvertContentResponse } from '@/types/api';

export async function POST(request: NextRequest) {
  try {
    const body: ConvertContentRequest = await request.json();
    const converter = new ContentConverter();
    
    const result = await converter.convert(body.content, body.type, body.options);
    
    const response: ApiResponse<ConvertContentResponse> = {
      success: true,
      data: result,
      message: '内容转换成功',
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: '内容转换失败',
      message: error instanceof Error ? error.message : '未知错误',
    };

    return NextResponse.json(response, { status: 500 });
  }
}
```

### 3. 文件上传实现

```typescript
// src/app/api/upload/image/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: '没有找到文件' },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { success: false, error: '只支持图片文件' },
        { status: 400 }
      );
    }

    // 生成文件名
    const timestamp = Date.now();
    const filename = `${timestamp}-${file.name}`;
    const uploadDir = join(process.cwd(), 'public', 'uploads');
    const filepath = join(uploadDir, filename);

    // 确保上传目录存在
    await mkdir(uploadDir, { recursive: true });

    // 处理图片
    const buffer = Buffer.from(await file.arrayBuffer());
    const processedImage = await sharp(buffer)
      .resize(1200, 1200, { fit: 'inside', withoutEnlargement: true })
      .jpeg({ quality: 85 })
      .toBuffer();

    // 保存文件
    await writeFile(filepath, processedImage);

    // 获取图片信息
    const metadata = await sharp(processedImage).metadata();

    const response = {
      success: true,
      data: {
        url: `/uploads/${filename}`,
        filename,
        size: processedImage.length,
        dimensions: {
          width: metadata.width || 0,
          height: metadata.height || 0,
        },
      },
      message: '图片上传成功',
    };

    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: '图片上传失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
```

## 🔒 错误处理

### 统一错误响应格式

```typescript
// src/lib/api/error-handler.ts
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: unknown): ApiResponse {
  if (error instanceof ApiError) {
    return {
      success: false,
      error: error.message,
      message: error.code,
    };
  }

  if (error instanceof Error) {
    return {
      success: false,
      error: '服务器内部错误',
      message: error.message,
    };
  }

  return {
    success: false,
    error: '未知错误',
  };
}
```

## 🔗 相关文档

- [02-数据库设计](./02-database-design.md) - 数据模型设计
- [03-编辑器实现](./03-editor-implementation.md) - 编辑器集成
- [05-前端界面](./05-frontend-interface.md) - 前端接口调用

---

**下一步**: 阅读 [05-前端界面](./05-frontend-interface.md) 了解前端界面实现
