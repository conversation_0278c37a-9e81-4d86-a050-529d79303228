# 11-故障排除

## 📋 概述

本文档提供微信公众号文章工作流程优化系统的故障排除指南，包括常见问题、解决方案、错误处理和回滚机制。

## 🚨 常见问题分类

### 1. 内容转换问题

#### 问题：HTML/Markdown 转换失败
**症状：**
- 粘贴内容后无法识别格式
- 转换后组件丢失或错误
- 特殊字符显示异常

**解决方案：**
```typescript
// 诊断步骤
export class ConversionDiagnostic {
  diagnoseConversionFailure(content: string): DiagnosticResult {
    const issues: Issue[] = [];
    
    // 1. 检查内容编码
    if (!this.isValidUTF8(content)) {
      issues.push({
        type: 'encoding',
        message: '内容编码不是有效的UTF-8',
        solution: '请确保内容使用UTF-8编码'
      });
    }
    
    // 2. 检查HTML结构
    if (content.includes('<') && !this.isValidHTML(content)) {
      issues.push({
        type: 'html_structure',
        message: 'HTML结构不完整或有语法错误',
        solution: '检查HTML标签是否正确闭合'
      });
    }
    
    // 3. 检查特殊字符
    const specialChars = this.findProblematicChars(content);
    if (specialChars.length > 0) {
      issues.push({
        type: 'special_chars',
        message: `发现问题字符: ${specialChars.join(', ')}`,
        solution: '替换或转义特殊字符'
      });
    }
    
    return { issues, hasErrors: issues.length > 0 };
  }

  private isValidHTML(html: string): boolean {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      return !doc.querySelector('parsererror');
    } catch {
      return false;
    }
  }

  private findProblematicChars(content: string): string[] {
    const problematic = [];
    const patterns = [
      /[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, // 控制字符
      /[\uFFFE\uFFFF]/g, // 非字符
      /[\uD800-\uDFFF]/g, // 代理对
    ];
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        problematic.push(...matches);
      }
    });
    
    return [...new Set(problematic)];
  }
}
```

#### 问题：组件映射错误
**症状：**
- 应该转换为HighlightBox的内容变成普通段落
- 代码块没有语法高亮
- 图片没有转换为ImageContainer

**解决方案：**
```typescript
// 组件映射调试器
export class ComponentMappingDebugger {
  debugMapping(element: Element): MappingDebugInfo {
    const rules = COMPONENT_RULES;
    const matchedRules: ComponentRule[] = [];
    const failedRules: { rule: ComponentRule; reason: string }[] = [];
    
    rules.forEach(rule => {
      try {
        if (typeof rule.pattern === 'function') {
          if (rule.pattern(element)) {
            matchedRules.push(rule);
          } else {
            failedRules.push({
              rule,
              reason: '模式函数返回false'
            });
          }
        } else {
          if (element.matches(rule.pattern.source)) {
            matchedRules.push(rule);
          } else {
            failedRules.push({
              rule,
              reason: '正则表达式不匹配'
            });
          }
        }
      } catch (error) {
        failedRules.push({
          rule,
          reason: `执行错误: ${error.message}`
        });
      }
    });
    
    return {
      element: {
        tagName: element.tagName,
        className: element.className,
        textContent: element.textContent?.substring(0, 100),
        attributes: this.getElementAttributes(element)
      },
      matchedRules: matchedRules.map(r => r.name),
      failedRules,
      recommendation: this.generateMappingRecommendation(element, matchedRules, failedRules)
    };
  }

  private generateMappingRecommendation(
    element: Element,
    matched: ComponentRule[],
    failed: { rule: ComponentRule; reason: string }[]
  ): string {
    if (matched.length === 0) {
      return '没有匹配的组件规则，将作为普通HTML元素处理';
    }
    
    if (matched.length > 1) {
      return `匹配了多个规则: ${matched.map(r => r.name).join(', ')}，将使用优先级最高的规则`;
    }
    
    return `成功匹配规则: ${matched[0].name}`;
  }
}
```

### 2. 样式一致性问题

#### 问题：编辑器预览与最终效果不一致
**症状：**
- 颜色显示不同
- 字体大小不匹配
- 布局错位

**解决方案：**
```typescript
// 样式一致性检查器
export class StyleConsistencyChecker {
  async checkConsistency(
    editorElement: HTMLElement,
    previewElement: HTMLElement
  ): Promise<ConsistencyReport> {
    const editorStyles = this.extractStyles(editorElement);
    const previewStyles = this.extractStyles(previewElement);
    
    const differences = this.compareStyles(editorStyles, previewStyles);
    const criticalDifferences = differences.filter(d => d.severity === 'critical');
    
    if (criticalDifferences.length > 0) {
      // 自动修复关键差异
      await this.autoFixCriticalDifferences(previewElement, criticalDifferences);
    }
    
    return {
      isConsistent: differences.length === 0,
      differences,
      autoFixed: criticalDifferences.length,
      recommendations: this.generateStyleRecommendations(differences)
    };
  }

  private async autoFixCriticalDifferences(
    element: HTMLElement,
    differences: StyleDifference[]
  ): Promise<void> {
    differences.forEach(diff => {
      switch (diff.property) {
        case 'color':
          element.style.color = diff.expectedValue;
          break;
        case 'font-size':
          element.style.fontSize = diff.expectedValue;
          break;
        case 'background-color':
          element.style.backgroundColor = diff.expectedValue;
          break;
        // 其他关键样式属性
      }
    });
  }
}
```

### 3. 微信编辑器兼容性问题

#### 问题：复制到微信后样式丢失
**症状：**
- 赛博朋克颜色变成默认颜色
- 特殊效果消失
- 布局错乱

**解决方案：**
```typescript
// 微信兼容性修复器
export class WechatCompatibilityFixer {
  async fixCompatibilityIssues(html: string): Promise<string> {
    let fixedHTML = html;
    
    // 1. 修复CSS变量问题
    fixedHTML = this.fixCSSVariables(fixedHTML);
    
    // 2. 修复不支持的CSS属性
    fixedHTML = this.fixUnsupportedCSS(fixedHTML);
    
    // 3. 修复颜色格式问题
    fixedHTML = this.fixColorFormats(fixedHTML);
    
    // 4. 修复布局问题
    fixedHTML = this.fixLayoutIssues(fixedHTML);
    
    return fixedHTML;
  }

  private fixCSSVariables(html: string): string {
    const variableMap = {
      'var(--cyberpunk-primary)': '#00d4ff',
      'var(--cyberpunk-secondary)': '#ff6bb3',
      'var(--cyberpunk-accent)': '#39ff14',
      // ... 其他变量映射
    };
    
    let fixed = html;
    Object.entries(variableMap).forEach(([variable, value]) => {
      fixed = fixed.replace(new RegExp(variable, 'g'), value);
    });
    
    return fixed;
  }

  private fixUnsupportedCSS(html: string): string {
    const dom = new DOMParser().parseFromString(html, 'text/html');
    const elements = dom.querySelectorAll('*');
    
    elements.forEach(element => {
      const style = (element as HTMLElement).style;
      
      // 移除不支持的属性
      WECHAT_UNSUPPORTED_CSS.forEach(property => {
        style.removeProperty(property);
      });
      
      // 转换特殊效果
      this.convertSpecialEffects(element as HTMLElement);
    });
    
    return dom.body.innerHTML;
  }

  private convertSpecialEffects(element: HTMLElement): void {
    // 将box-shadow转换为border
    const boxShadow = element.style.boxShadow;
    if (boxShadow && boxShadow.includes('rgba(0, 212, 255')) {
      element.style.border = '2px solid #00d4ff';
      element.style.boxShadow = '';
    }
    
    // 将text-shadow转换为font-weight
    const textShadow = element.style.textShadow;
    if (textShadow) {
      element.style.fontWeight = 'bold';
      element.style.textShadow = '';
    }
  }
}
```

### 4. 数据库相关问题

#### 问题：文章保存失败
**症状：**
- 保存时出现错误提示
- 数据丢失
- 连接超时

**解决方案：**
```typescript
// 数据库错误处理器
export class DatabaseErrorHandler {
  async handleSaveError(error: Error, articleData: ArticleFormData): Promise<SaveResult> {
    console.error('文章保存失败:', error);
    
    // 1. 尝试本地缓存
    await this.saveToLocalCache(articleData);
    
    // 2. 分析错误类型
    const errorType = this.analyzeError(error);
    
    // 3. 根据错误类型采取不同策略
    switch (errorType) {
      case 'connection':
        return this.handleConnectionError(articleData);
      case 'validation':
        return this.handleValidationError(error, articleData);
      case 'constraint':
        return this.handleConstraintError(error, articleData);
      default:
        return this.handleUnknownError(error, articleData);
    }
  }

  private async saveToLocalCache(articleData: ArticleFormData): Promise<void> {
    const cacheKey = `article_backup_${Date.now()}`;
    const cacheData = {
      ...articleData,
      timestamp: new Date().toISOString(),
      version: '1.0'
    };
    
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    
    // 通知用户已保存到本地
    this.notifyLocalSave(cacheKey);
  }

  private async handleConnectionError(articleData: ArticleFormData): Promise<SaveResult> {
    // 实现重试机制
    const maxRetries = 3;
    let retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        const result = await this.retrySave(articleData);
        return { success: true, data: result };
      } catch (error) {
        retryCount++;
        if (retryCount === maxRetries) {
          return {
            success: false,
            error: '连接失败，已保存到本地缓存',
            localBackup: true
          };
        }
      }
    }
    
    return { success: false, error: '未知错误' };
  }
}
```

## 🔄 回滚机制

### 1. 版本回滚

```typescript
// 版本管理器
export class VersionManager {
  async createSnapshot(articleId: string): Promise<string> {
    const article = await prisma.article.findUnique({
      where: { id: articleId },
      include: { images: true }
    });
    
    if (!article) throw new Error('文章不存在');
    
    // 创建版本快照
    const version = await prisma.articleVersion.create({
      data: {
        articleId,
        version: await this.getNextVersionNumber(articleId),
        title: article.title,
        contentJson: article.contentJson,
        changeLog: '自动快照',
        createdAt: new Date()
      }
    });
    
    return version.id;
  }

  async rollbackToVersion(articleId: string, versionId: string): Promise<boolean> {
    try {
      const version = await prisma.articleVersion.findUnique({
        where: { id: versionId }
      });
      
      if (!version || version.articleId !== articleId) {
        throw new Error('版本不存在或不匹配');
      }
      
      // 创建当前状态的备份
      await this.createSnapshot(articleId);
      
      // 回滚到指定版本
      await prisma.article.update({
        where: { id: articleId },
        data: {
          title: version.title,
          contentJson: version.contentJson,
          updatedAt: new Date()
        }
      });
      
      return true;
    } catch (error) {
      console.error('回滚失败:', error);
      return false;
    }
  }
}
```

### 2. 数据恢复

```typescript
// 数据恢复服务
export class DataRecoveryService {
  async recoverFromLocalBackup(): Promise<ArticleFormData[]> {
    const backups: ArticleFormData[] = [];
    
    // 扫描localStorage中的备份
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith('article_backup_')) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const backup = JSON.parse(data);
            backups.push(backup);
          }
        } catch (error) {
          console.error(`恢复备份失败: ${key}`, error);
        }
      }
    }
    
    return backups.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  async restoreFromBackup(backup: ArticleFormData): Promise<boolean> {
    try {
      // 验证备份数据
      const isValid = await this.validateBackupData(backup);
      if (!isValid) {
        throw new Error('备份数据无效');
      }
      
      // 恢复到数据库
      const article = await prisma.article.create({
        data: {
          slug: backup.slug + '_recovered',
          title: backup.title + ' (已恢复)',
          excerpt: backup.excerpt,
          contentJson: JSON.stringify(backup.content),
          status: 'DRAFT',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      return true;
    } catch (error) {
      console.error('恢复失败:', error);
      return false;
    }
  }
}
```

## 🔧 性能问题排查

### 1. 性能监控

```typescript
// 性能监控器
export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  
  startMeasure(name: string): void {
    this.metrics.set(name, {
      startTime: performance.now(),
      endTime: 0,
      duration: 0
    });
  }
  
  endMeasure(name: string): number {
    const metric = this.metrics.get(name);
    if (!metric) return 0;
    
    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    
    // 记录慢操作
    if (metric.duration > 1000) {
      console.warn(`慢操作检测: ${name} 耗时 ${metric.duration.toFixed(2)}ms`);
    }
    
    return metric.duration;
  }
  
  getReport(): PerformanceReport {
    const report: PerformanceReport = {
      totalOperations: this.metrics.size,
      slowOperations: [],
      averageDuration: 0
    };
    
    let totalDuration = 0;
    this.metrics.forEach((metric, name) => {
      totalDuration += metric.duration;
      
      if (metric.duration > 1000) {
        report.slowOperations.push({
          name,
          duration: metric.duration
        });
      }
    });
    
    report.averageDuration = totalDuration / this.metrics.size;
    
    return report;
  }
}
```

### 2. 内存泄漏检测

```typescript
// 内存监控器
export class MemoryMonitor {
  private observers: Set<MutationObserver> = new Set();
  private intervals: Set<number> = new Set();
  
  checkMemoryUsage(): MemoryInfo {
    const memory = (performance as any).memory;
    
    if (memory) {
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      };
    }
    
    return { usage: 0 };
  }
  
  detectLeaks(): LeakDetectionResult {
    const memoryInfo = this.checkMemoryUsage();
    const leaks: MemoryLeak[] = [];
    
    // 检测未清理的观察器
    if (this.observers.size > 10) {
      leaks.push({
        type: 'observer',
        count: this.observers.size,
        description: '过多的MutationObserver未清理'
      });
    }
    
    // 检测未清理的定时器
    if (this.intervals.size > 5) {
      leaks.push({
        type: 'interval',
        count: this.intervals.size,
        description: '过多的定时器未清理'
      });
    }
    
    // 检测内存使用率
    if (memoryInfo.usage > 80) {
      leaks.push({
        type: 'memory',
        count: memoryInfo.usage,
        description: '内存使用率过高'
      });
    }
    
    return {
      hasLeaks: leaks.length > 0,
      leaks,
      memoryInfo
    };
  }
  
  cleanup(): void {
    // 清理所有观察器
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    
    // 清理所有定时器
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
  }
}
```

## 📞 技术支持流程

### 1. 问题报告模板

```typescript
// 问题报告生成器
export class IssueReporter {
  generateReport(error: Error, context: any): IssueReport {
    return {
      timestamp: new Date().toISOString(),
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        memory: this.getMemoryInfo(),
        performance: this.getPerformanceInfo()
      },
      systemInfo: {
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      },
      applicationState: context
    };
  }
  
  async submitReport(report: IssueReport): Promise<boolean> {
    try {
      // 发送到错误收集服务
      const response = await fetch('/api/error-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      });
      
      return response.ok;
    } catch (error) {
      console.error('提交错误报告失败:', error);
      return false;
    }
  }
}
```

## 🔗 相关文档

- [06-内容转换机制](./06-content-conversion.md) - 转换问题排查
- [07-样式一致性保证](./07-style-consistency.md) - 样式问题解决
- [08-微信编辑器集成](./08-wechat-integration.md) - 兼容性问题处理
- [09-实施指南](./09-implementation-guide.md) - 实施过程问题

---

**说明**: 遇到问题时，请按照本文档的排查步骤进行诊断，如果问题仍未解决，请使用问题报告工具生成详细报告。
