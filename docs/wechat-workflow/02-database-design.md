# 02-数据库设计

## 📋 概述

本文档详细描述基于 Prisma + PostgreSQL 的数据库设计方案，包括数据模型、关系设计、迁移策略和性能优化。

## 🗄 技术选型

### PostgreSQL vs SQLite
**选择 PostgreSQL 的原因:**
- 更好的并发性能和事务支持
- 丰富的数据类型（JSON、数组等）
- 强大的全文搜索功能
- 更好的扩展性和生产环境支持
- Prisma 对 PostgreSQL 的优化更好

### Prisma ORM 优势
- 类型安全的数据库访问
- 自动生成 TypeScript 类型
- 强大的迁移系统
- 直观的查询API
- 内置连接池和缓存

## 📊 数据模型设计

### Prisma Schema 定义

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 文章主表
model Article {
  id          String   @id @default(cuid())
  slug        String   @unique
  title       String
  excerpt     String?
  
  // 内容存储
  contentJson String   // TipTap JSON 格式
  contentHtml String?  // 缓存的 HTML（用于快速预览）
  
  // 元数据
  metadata    Json?    // 扩展元数据（标签、作者等）
  
  // 状态管理
  status      ArticleStatus @default(DRAFT)
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?
  
  // 关联关系
  images      ArticleImage[]
  versions    ArticleVersion[]
  exports     ArticleExport[]
  
  // 索引
  @@map("articles")
  @@index([slug])
  @@index([status])
  @@index([createdAt])
  @@index([publishedAt])
}

// 文章状态枚举
enum ArticleStatus {
  DRAFT     // 草稿
  PUBLISHED // 已发布
  ARCHIVED  // 已归档
}

// 文章图片资源
model ArticleImage {
  id           String   @id @default(cuid())
  articleId    String
  
  // 文件信息
  filename     String
  originalName String
  filePath     String
  fileSize     Int
  mimeType     String
  
  // 图片属性
  width        Int?
  height       Int?
  alt          String?
  caption      String?
  
  // 时间戳
  createdAt    DateTime @default(now())
  
  // 关联关系
  article      Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)
  
  @@map("article_images")
  @@index([articleId])
}

// 文章版本历史
model ArticleVersion {
  id          String   @id @default(cuid())
  articleId   String
  
  // 版本信息
  version     Int
  title       String
  contentJson String
  
  // 变更信息
  changeLog   String?
  createdBy   String?  // 未来扩展用户系统
  
  // 时间戳
  createdAt   DateTime @default(now())
  
  // 关联关系
  article     Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)
  
  @@map("article_versions")
  @@unique([articleId, version])
  @@index([articleId])
}

// 文章导出记录
model ArticleExport {
  id          String     @id @default(cuid())
  articleId   String
  
  // 导出信息
  format      ExportFormat
  content     String     // 导出的内容
  metadata    Json?      // 导出时的配置信息
  
  // 时间戳
  createdAt   DateTime   @default(now())
  expiresAt   DateTime?  // 导出内容过期时间
  
  // 关联关系
  article     Article    @relation(fields: [articleId], references: [id], onDelete: Cascade)
  
  @@map("article_exports")
  @@index([articleId])
  @@index([createdAt])
}

// 导出格式枚举
enum ExportFormat {
  WECHAT_HTML  // 微信公众号 HTML
  MARKDOWN     // Markdown 格式
  PDF          // PDF 格式（未来扩展）
  DOCX         // Word 格式（未来扩展）
}

// 系统配置表
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       Json
  description String?
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("system_configs")
}

// 操作日志表
model OperationLog {
  id          String      @id @default(cuid())
  
  // 操作信息
  action      String      // 操作类型
  resource    String      // 资源类型
  resourceId  String?     // 资源ID
  
  // 操作详情
  details     Json?       // 操作详细信息
  userAgent   String?     // 用户代理
  ipAddress   String?     // IP地址
  
  // 时间戳
  createdAt   DateTime    @default(now())
  
  @@map("operation_logs")
  @@index([action])
  @@index([resource])
  @@index([createdAt])
}
```

## 🔧 数据库配置

### 环境变量配置

```bash
# .env
DATABASE_URL="postgresql://username:password@localhost:5432/wechat_workflow?schema=public"
DIRECT_URL="postgresql://username:password@localhost:5432/wechat_workflow?schema=public"

# 开发环境
DATABASE_URL_DEV="postgresql://dev_user:dev_pass@localhost:5432/wechat_workflow_dev?schema=public"

# 测试环境
DATABASE_URL_TEST="postgresql://test_user:test_pass@localhost:5432/wechat_workflow_test?schema=public"
```

### Prisma 客户端配置

```typescript
// lib/prisma/client.ts
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// 优雅关闭
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});
```

## 📈 数据迁移策略

### 1. 初始化迁移

```bash
# 初始化 Prisma
npx prisma init

# 生成初始迁移
npx prisma migrate dev --name init

# 生成 Prisma 客户端
npx prisma generate
```

### 2. 从现有 TSX 文件迁移

```typescript
// scripts/migrate-tsx-to-db.ts
import { prisma } from '@/lib/prisma/client';
import { readTsxArticles } from '@/lib/utils/tsx-reader';
import { convertTsxToTipTap } from '@/lib/converters/tsx-to-tiptap';

async function migrateTsxArticles() {
  console.log('开始迁移 TSX 文章到数据库...');
  
  const tsxArticles = await readTsxArticles();
  
  for (const tsxArticle of tsxArticles) {
    try {
      // 转换 TSX 内容为 TipTap JSON
      const tiptapContent = await convertTsxToTipTap(tsxArticle.content);
      
      // 创建文章记录
      const article = await prisma.article.create({
        data: {
          slug: tsxArticle.slug,
          title: tsxArticle.metadata.title,
          excerpt: tsxArticle.metadata.excerpt,
          contentJson: JSON.stringify(tiptapContent),
          status: 'PUBLISHED',
          publishedAt: new Date(tsxArticle.metadata.date),
          metadata: {
            originalFormat: 'tsx',
            migrationDate: new Date().toISOString(),
            ...tsxArticle.metadata,
          },
        },
      });
      
      // 迁移图片资源
      if (tsxArticle.images.length > 0) {
        await prisma.articleImage.createMany({
          data: tsxArticle.images.map(img => ({
            articleId: article.id,
            filename: img.filename,
            originalName: img.originalName,
            filePath: img.path,
            fileSize: img.size,
            mimeType: img.mimeType,
            alt: img.alt,
            caption: img.caption,
          })),
        });
      }
      
      console.log(`✅ 迁移完成: ${article.title}`);
    } catch (error) {
      console.error(`❌ 迁移失败: ${tsxArticle.slug}`, error);
    }
  }
  
  console.log('迁移完成！');
}

// 执行迁移
migrateTsxArticles().catch(console.error);
```

### 3. 种子数据

```typescript
// prisma/seed.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // 创建系统配置
  await prisma.systemConfig.createMany({
    data: [
      {
        key: 'site_title',
        value: '硅基茶馆2077',
        description: '网站标题',
      },
      {
        key: 'wechat_export_settings',
        value: {
          includeStyles: true,
          optimizeImages: true,
          inlineCSS: true,
        },
        description: '微信导出设置',
      },
      {
        key: 'editor_settings',
        value: {
          autoSave: true,
          autoSaveInterval: 30000,
          enableVersionHistory: true,
        },
        description: '编辑器设置',
      },
    ],
    skipDuplicates: true,
  });

  // 创建示例文章
  const sampleArticle = await prisma.article.create({
    data: {
      slug: '2025-01-15',
      title: '微信公众号工作流程优化示例',
      excerpt: '这是一篇展示新工作流程的示例文章',
      contentJson: JSON.stringify({
        type: 'doc',
        content: [
          {
            type: 'heading',
            attrs: { level: 2 },
            content: [{ type: 'text', text: '欢迎使用新的编辑器' }],
          },
          {
            type: 'paragraph',
            content: [
              { type: 'text', text: '这是一个使用 TipTap 编辑器创建的示例文章。' },
            ],
          },
        ],
      }),
      status: 'PUBLISHED',
      publishedAt: new Date(),
      metadata: {
        tags: ['示例', '教程'],
        author: '系统',
      },
    },
  });

  console.log('种子数据创建完成');
  console.log('示例文章:', sampleArticle);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

## 🚀 性能优化

### 1. 数据库索引策略

```sql
-- 复合索引优化查询
CREATE INDEX idx_articles_status_published ON articles(status, published_at DESC);
CREATE INDEX idx_articles_slug_status ON articles(slug, status);

-- 全文搜索索引
CREATE INDEX idx_articles_title_search ON articles USING gin(to_tsvector('english', title));
CREATE INDEX idx_articles_content_search ON articles USING gin(to_tsvector('english', content_html));
```

### 2. 查询优化

```typescript
// lib/services/article-service.ts
export class ArticleService {
  // 优化的文章列表查询
  async getArticleList(params: {
    page?: number;
    limit?: number;
    status?: ArticleStatus;
    search?: string;
  }) {
    const { page = 1, limit = 10, status, search } = params;
    
    const where = {
      ...(status && { status }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { excerpt: { contains: search, mode: 'insensitive' } },
        ],
      }),
    };

    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        select: {
          id: true,
          slug: true,
          title: true,
          excerpt: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          publishedAt: true,
          _count: {
            select: {
              images: true,
              versions: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.article.count({ where }),
    ]);

    return {
      articles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  // 优化的文章详情查询
  async getArticleBySlug(slug: string) {
    return prisma.article.findUnique({
      where: { slug },
      include: {
        images: {
          orderBy: { createdAt: 'asc' },
        },
        versions: {
          select: {
            id: true,
            version: true,
            changeLog: true,
            createdAt: true,
          },
          orderBy: { version: 'desc' },
          take: 5, // 只返回最近5个版本
        },
      },
    });
  }
}
```

### 3. 连接池配置

```typescript
// lib/prisma/client.ts
export const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // 连接池配置
  __internal: {
    engine: {
      connectionLimit: 10,
      poolTimeout: 10000,
    },
  },
});
```

## 🔄 数据备份和恢复

### 1. 自动备份脚本

```bash
#!/bin/bash
# scripts/backup-db.sh

DB_NAME="wechat_workflow"
BACKUP_DIR="/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "备份完成: backup_$DATE.sql.gz"
```

### 2. 数据恢复

```bash
# 恢复数据库
psql -d wechat_workflow < backup_20250115_120000.sql
```

## 🔗 相关文档

- [01-核心架构设计](./01-core-architecture.md) - 系统整体架构
- [03-编辑器实现](./03-editor-implementation.md) - 编辑器与数据库的集成
- [09-实施指南](./09-implementation-guide.md) - 数据库部署实施

---

**下一步**: 阅读 [03-编辑器实现](./03-editor-implementation.md) 了解编辑器实现细节
