# 08-微信编辑器集成

## 📋 概述

本文档详细说明一键复制到微信公众号编辑器的技术实现细节，包括样式内联、兼容性处理、图片路径转换等关键技术。

## 🎯 集成目标

### 核心要求
- **一键复制**: 用户点击按钮即可复制完整内容
- **样式保持**: 赛博朋克样式完整保留
- **兼容性**: 完美适配微信编辑器限制
- **图片处理**: 自动处理图片路径和优化

## 🔧 微信编辑器限制分析

### 1. 支持的CSS属性

```typescript
// lib/wechat/supported-styles.ts
export const WECHAT_SUPPORTED_CSS = {
  // 文本样式
  text: [
    'color', 'font-size', 'font-weight', 'font-style', 'font-family',
    'text-align', 'text-decoration', 'line-height', 'letter-spacing',
    'text-shadow', 'text-indent'
  ],
  
  // 布局样式
  layout: [
    'margin', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left',
    'padding', 'padding-top', 'padding-right', 'padding-bottom', 'padding-left',
    'width', 'height', 'max-width', 'max-height', 'min-width', 'min-height'
  ],
  
  // 背景样式
  background: [
    'background-color', 'background-image', 'background-repeat',
    'background-position', 'background-size'
  ],
  
  // 边框样式
  border: [
    'border', 'border-top', 'border-right', 'border-bottom', 'border-left',
    'border-color', 'border-style', 'border-width', 'border-radius'
  ],
  
  // 显示样式
  display: [
    'display', 'position', 'top', 'right', 'bottom', 'left',
    'z-index', 'overflow', 'visibility', 'opacity'
  ]
};

export const WECHAT_UNSUPPORTED_CSS = [
  // 不支持的CSS属性
  'transform', 'transition', 'animation', 'filter', 'backdrop-filter',
  'clip-path', 'mask', 'mix-blend-mode', 'object-fit', 'object-position',
  'grid', 'flex', 'flexbox', 'css-variables', 'calc()', 'var()',
  'box-shadow' // 部分支持，需要简化
];
```

### 2. 颜色格式限制

```typescript
// lib/wechat/color-processor.ts
export class WechatColorProcessor {
  // 微信支持的颜色格式
  private supportedFormats = ['hex', 'rgb', 'rgba', 'named'];
  
  processColor(color: string): string {
    // 转换CSS变量为具体颜色值
    if (color.includes('var(')) {
      return this.resolveCSSVariable(color);
    }
    
    // 转换HSL为RGB
    if (color.includes('hsl')) {
      return this.hslToRgb(color);
    }
    
    // 简化复杂的颜色函数
    if (color.includes('color-mix') || color.includes('oklch')) {
      return this.simplifyColorFunction(color);
    }
    
    return color;
  }

  private resolveCSSVariable(color: string): string {
    const variableMap = {
      'var(--cyberpunk-primary)': '#00d4ff',
      'var(--cyberpunk-secondary)': '#ff6bb3',
      'var(--cyberpunk-accent)': '#39ff14',
      'var(--cyberpunk-text-primary)': '#333333',
      'var(--cyberpunk-text-secondary)': '#666666',
    };
    
    return color.replace(/var\((--[\w-]+)\)/g, (match, varName) => {
      return variableMap[match] || '#000000';
    });
  }

  private hslToRgb(hsl: string): string {
    // HSL转RGB的实现
    const match = hsl.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
    if (!match) return hsl;
    
    const [, h, s, l] = match.map(Number);
    const rgb = this.hslToRgbValues(h, s / 100, l / 100);
    
    return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
  }
}
```

## 🎨 样式内联处理器

### 1. CSS内联转换器

```typescript
// lib/wechat/style-inliner.ts
export class WechatStyleInliner {
  private colorProcessor = new WechatColorProcessor();
  private supportedStyles = WECHAT_SUPPORTED_CSS;
  
  async inlineStyles(html: string): Promise<string> {
    const dom = new DOMParser().parseFromString(html, 'text/html');
    
    // 处理所有元素
    const elements = dom.querySelectorAll('*');
    for (const element of elements) {
      await this.processElement(element as HTMLElement);
    }
    
    // 移除style标签和link标签
    this.removeStyleElements(dom);
    
    return dom.body.innerHTML;
  }

  private async processElement(element: HTMLElement): Promise<void> {
    // 获取计算样式
    const computedStyles = this.getComputedStyles(element);
    
    // 过滤支持的样式
    const supportedStyles = this.filterSupportedStyles(computedStyles);
    
    // 优化样式值
    const optimizedStyles = this.optimizeStyleValues(supportedStyles);
    
    // 应用内联样式
    this.applyInlineStyles(element, optimizedStyles);
    
    // 处理特殊情况
    this.handleSpecialCases(element);
  }

  private filterSupportedStyles(styles: CSSStyleDeclaration): Record<string, string> {
    const filtered: Record<string, string> = {};
    const allSupported = [
      ...this.supportedStyles.text,
      ...this.supportedStyles.layout,
      ...this.supportedStyles.background,
      ...this.supportedStyles.border,
      ...this.supportedStyles.display,
    ];
    
    allSupported.forEach(property => {
      const value = styles.getPropertyValue(property);
      if (value && value !== 'initial' && value !== 'inherit') {
        filtered[property] = value;
      }
    });
    
    return filtered;
  }

  private optimizeStyleValues(styles: Record<string, string>): Record<string, string> {
    const optimized: Record<string, string> = {};
    
    Object.entries(styles).forEach(([property, value]) => {
      let optimizedValue = value;
      
      // 处理颜色值
      if (this.isColorProperty(property)) {
        optimizedValue = this.colorProcessor.processColor(value);
      }
      
      // 处理阴影效果（简化为边框）
      if (property === 'box-shadow') {
        optimizedValue = this.convertShadowToBorder(value);
        property = 'border';
      }
      
      // 处理渐变背景（转换为纯色）
      if (property === 'background-image' && value.includes('gradient')) {
        optimizedValue = this.extractGradientColor(value);
        property = 'background-color';
      }
      
      // 移除不支持的函数
      optimizedValue = this.removeCSSFunctions(optimizedValue);
      
      optimized[property] = optimizedValue;
    });
    
    return optimized;
  }

  private convertShadowToBorder(shadow: string): string {
    // 将box-shadow转换为border
    // 提取阴影颜色作为边框颜色
    const colorMatch = shadow.match(/rgba?\([^)]+\)|#[a-fA-F0-9]{3,6}/);
    const color = colorMatch ? colorMatch[0] : '#00d4ff';
    
    return `1px solid ${color}`;
  }

  private extractGradientColor(gradient: string): string {
    // 从渐变中提取主要颜色
    const colorMatch = gradient.match(/rgba?\([^)]+\)|#[a-fA-F0-9]{3,6}/);
    return colorMatch ? colorMatch[0] : '#00d4ff';
  }
}
```

### 2. 特殊效果处理

```typescript
// lib/wechat/effect-processor.ts
export class WechatEffectProcessor {
  processGlowEffect(element: HTMLElement): void {
    // 将发光效果转换为边框和背景
    const glowColor = this.extractGlowColor(element);
    
    if (glowColor) {
      // 添加边框模拟发光
      element.style.border = `2px solid ${glowColor}`;
      element.style.borderRadius = '4px';
      
      // 添加背景色增强效果
      const bgColor = this.lightenColor(glowColor, 0.9);
      element.style.backgroundColor = bgColor;
    }
  }

  processTextShadow(element: HTMLElement): void {
    const textShadow = element.style.textShadow;
    if (textShadow) {
      // 提取阴影颜色
      const colorMatch = textShadow.match(/rgba?\([^)]+\)|#[a-fA-F0-9]{3,6}/);
      if (colorMatch) {
        // 将文字阴影转换为文字颜色增强
        const shadowColor = colorMatch[0];
        element.style.color = shadowColor;
        element.style.fontWeight = 'bold';
      }
      
      // 移除不支持的text-shadow
      element.style.textShadow = '';
    }
  }

  processAnimation(element: HTMLElement): void {
    // 移除动画，保留最终状态
    element.style.animation = '';
    element.style.transition = '';
    
    // 如果有动画类，应用静态样式
    if (element.classList.contains('cyberpunk-pulse')) {
      element.style.opacity = '1';
      element.style.transform = '';
    }
  }

  private extractGlowColor(element: HTMLElement): string | null {
    const boxShadow = element.style.boxShadow;
    if (boxShadow) {
      const colorMatch = boxShadow.match(/rgba?\([^)]+\)|#[a-fA-F0-9]{3,6}/);
      return colorMatch ? colorMatch[0] : null;
    }
    return null;
  }

  private lightenColor(color: string, opacity: number): string {
    // 简化实现：将颜色转换为半透明背景
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
    return color;
  }
}
```

## 🖼 图片处理系统

### 1. 图片路径转换器

```typescript
// lib/wechat/image-processor.ts
export class WechatImageProcessor {
  private baseUrl: string;
  
  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl || window.location.origin;
  }

  async processImages(html: string): Promise<string> {
    const dom = new DOMParser().parseFromString(html, 'text/html');
    const images = dom.querySelectorAll('img');
    
    for (const img of images) {
      await this.processImage(img);
    }
    
    return dom.body.innerHTML;
  }

  private async processImage(img: HTMLImageElement): Promise<void> {
    const src = img.getAttribute('src');
    if (!src) return;
    
    // 处理相对路径
    if (src.startsWith('/')) {
      img.src = this.baseUrl + src;
    }
    
    // 处理本地图片（可选：上传到CDN）
    if (src.startsWith('/posts/')) {
      const cdnUrl = await this.uploadToCDN(src);
      if (cdnUrl) {
        img.src = cdnUrl;
      }
    }
    
    // 优化图片属性
    this.optimizeImageAttributes(img);
  }

  private optimizeImageAttributes(img: HTMLImageElement): void {
    // 确保图片响应式
    if (!img.style.maxWidth) {
      img.style.maxWidth = '100%';
    }
    
    if (!img.style.height) {
      img.style.height = 'auto';
    }
    
    // 添加微信编辑器友好的样式
    img.style.display = 'block';
    img.style.margin = '10px auto';
    
    // 确保alt属性存在
    if (!img.alt) {
      img.alt = '图片';
    }
  }

  private async uploadToCDN(localPath: string): Promise<string | null> {
    // 这里可以实现图片上传到CDN的逻辑
    // 暂时返回完整URL
    return this.baseUrl + localPath;
  }
}
```

### 2. 图片压缩优化

```typescript
// lib/wechat/image-optimizer.ts
export class WechatImageOptimizer {
  async optimizeImage(imageUrl: string): Promise<string> {
    try {
      // 获取图片
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      
      // 创建canvas进行压缩
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      return new Promise((resolve) => {
        img.onload = () => {
          // 计算压缩尺寸
          const { width, height } = this.calculateOptimalSize(img.width, img.height);
          
          canvas.width = width;
          canvas.height = height;
          
          // 绘制压缩图片
          ctx?.drawImage(img, 0, 0, width, height);
          
          // 转换为优化的格式
          canvas.toBlob((blob) => {
            if (blob) {
              const optimizedUrl = URL.createObjectURL(blob);
              resolve(optimizedUrl);
            } else {
              resolve(imageUrl);
            }
          }, 'image/jpeg', 0.8);
        };
        
        img.src = imageUrl;
      });
    } catch (error) {
      console.error('图片优化失败:', error);
      return imageUrl;
    }
  }

  private calculateOptimalSize(width: number, height: number): { width: number; height: number } {
    const maxWidth = 800; // 微信编辑器推荐最大宽度
    const maxHeight = 600;
    
    if (width <= maxWidth && height <= maxHeight) {
      return { width, height };
    }
    
    const aspectRatio = width / height;
    
    if (width > height) {
      return {
        width: maxWidth,
        height: Math.round(maxWidth / aspectRatio),
      };
    } else {
      return {
        width: Math.round(maxHeight * aspectRatio),
        height: maxHeight,
      };
    }
  }
}
```

## 📋 一键复制实现

### 1. 复制服务主类

```typescript
// lib/wechat/copy-service.ts
export class WechatCopyService {
  private styleInliner = new WechatStyleInliner();
  private imageProcessor = new WechatImageProcessor();
  private effectProcessor = new WechatEffectProcessor();
  
  async copyToClipboard(content: any): Promise<boolean> {
    try {
      // 1. 渲染TipTap内容为HTML
      const html = await this.renderTipTapToHTML(content);
      
      // 2. 处理图片
      const processedHTML = await this.imageProcessor.processImages(html);
      
      // 3. 内联样式
      const inlinedHTML = await this.styleInliner.inlineStyles(processedHTML);
      
      // 4. 处理特殊效果
      const finalHTML = this.processSpecialEffects(inlinedHTML);
      
      // 5. 复制到剪贴板
      await this.copyHTMLToClipboard(finalHTML);
      
      return true;
    } catch (error) {
      console.error('复制失败:', error);
      return false;
    }
  }

  private async renderTipTapToHTML(content: any): Promise<string> {
    // 使用TipTap的HTML渲染器
    const { generateHTML } = await import('@tiptap/html');
    const extensions = await this.getExtensions();
    
    return generateHTML(content, extensions);
  }

  private processSpecialEffects(html: string): string {
    const dom = new DOMParser().parseFromString(html, 'text/html');
    const elements = dom.querySelectorAll('*');
    
    elements.forEach(element => {
      const htmlElement = element as HTMLElement;
      
      // 处理发光效果
      this.effectProcessor.processGlowEffect(htmlElement);
      
      // 处理文字阴影
      this.effectProcessor.processTextShadow(htmlElement);
      
      // 移除动画
      this.effectProcessor.processAnimation(htmlElement);
    });
    
    return dom.body.innerHTML;
  }

  private async copyHTMLToClipboard(html: string): Promise<void> {
    // 创建富文本剪贴板项
    const clipboardItem = new ClipboardItem({
      'text/html': new Blob([html], { type: 'text/html' }),
      'text/plain': new Blob([this.htmlToText(html)], { type: 'text/plain' }),
    });
    
    await navigator.clipboard.write([clipboardItem]);
  }

  private htmlToText(html: string): string {
    const dom = new DOMParser().parseFromString(html, 'text/html');
    return dom.body.textContent || '';
  }
}
```

### 2. 复制按钮组件

```typescript
// components/wechat/CopyToWechatButton.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Copy, Check, AlertCircle } from 'lucide-react';
import { WechatCopyService } from '@/lib/wechat/copy-service';

interface CopyToWechatButtonProps {
  content: any; // TipTap JSON
  className?: string;
}

export function CopyToWechatButton({ content, className }: CopyToWechatButtonProps) {
  const [status, setStatus] = useState<'idle' | 'copying' | 'success' | 'error'>('idle');
  const [copyService] = useState(() => new WechatCopyService());

  const handleCopy = async () => {
    setStatus('copying');
    
    try {
      const success = await copyService.copyToClipboard(content);
      
      if (success) {
        setStatus('success');
        setTimeout(() => setStatus('idle'), 3000);
      } else {
        setStatus('error');
        setTimeout(() => setStatus('idle'), 3000);
      }
    } catch (error) {
      console.error('复制失败:', error);
      setStatus('error');
      setTimeout(() => setStatus('idle'), 3000);
    }
  };

  const getButtonContent = () => {
    switch (status) {
      case 'copying':
        return (
          <>
            <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full" />
            处理中...
          </>
        );
      case 'success':
        return (
          <>
            <Check className="h-4 w-4 mr-2" />
            复制成功
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="h-4 w-4 mr-2" />
            复制失败
          </>
        );
      default:
        return (
          <>
            <Copy className="h-4 w-4 mr-2" />
            复制到微信编辑器
          </>
        );
    }
  };

  return (
    <Button
      onClick={handleCopy}
      disabled={status === 'copying'}
      className={`
        bg-gradient-to-r from-cyan-500 to-blue-500 
        hover:from-cyan-600 hover:to-blue-600 
        text-white border-0 shadow-lg shadow-cyan-500/25 
        hover:shadow-cyan-500/40 transition-all duration-300 
        filter drop-shadow-[0_0_15px_rgba(0,212,255,0.3)]
        ${className}
      `}
    >
      {getButtonContent()}
    </Button>
  );
}
```

## 🔍 兼容性测试

### 1. 自动化测试

```typescript
// lib/wechat/compatibility-tester.ts
export class WechatCompatibilityTester {
  async testCompatibility(html: string): Promise<CompatibilityReport> {
    const issues: CompatibilityIssue[] = [];
    
    // 测试CSS兼容性
    const cssIssues = await this.testCSSCompatibility(html);
    issues.push(...cssIssues);
    
    // 测试图片兼容性
    const imageIssues = await this.testImageCompatibility(html);
    issues.push(...imageIssues);
    
    // 测试结构兼容性
    const structureIssues = await this.testStructureCompatibility(html);
    issues.push(...structureIssues);
    
    return {
      isCompatible: issues.length === 0,
      issues,
      score: this.calculateCompatibilityScore(issues),
      recommendations: this.generateRecommendations(issues),
    };
  }

  private async testCSSCompatibility(html: string): Promise<CompatibilityIssue[]> {
    const issues: CompatibilityIssue[] = [];
    const dom = new DOMParser().parseFromString(html, 'text/html');
    const elements = dom.querySelectorAll('*');
    
    elements.forEach(element => {
      const style = (element as HTMLElement).style;
      
      // 检查不支持的CSS属性
      WECHAT_UNSUPPORTED_CSS.forEach(property => {
        if (style.getPropertyValue(property)) {
          issues.push({
            type: 'css',
            severity: 'warning',
            message: `不支持的CSS属性: ${property}`,
            element: element.tagName,
            suggestion: `移除或替换 ${property} 属性`,
          });
        }
      });
    });
    
    return issues;
  }
}
```

## 🔗 相关文档

- [06-内容转换机制](./06-content-conversion.md) - 内容转换基础
- [07-样式一致性保证](./07-style-consistency.md) - 样式处理机制
- [09-实施指南](./09-implementation-guide.md) - 具体实施步骤
- [11-故障排除](./11-troubleshooting.md) - 常见问题解决

---

**下一步**: 阅读 [09-实施指南](./09-implementation-guide.md) 开始具体实施
