# TSX 工作流程优化指南

## 概述

本指南介绍了优化后的 TSX + Markdown 工作流程，用于创建微信公众号文章。虽然我们暂时没有使用 MDX，但通过组件化的方式，我们仍然可以大大简化文章创建过程。

## 工作流程

### 1. 文章撰写
- 在 Gemini 官网调研并撰写生成 Markdown 文章
- 将原始 Markdown 保存到 `content/YYYY-MM-DD.md`

### 2. TSX 文件创建
- 在 `src/app/posts/YYYY-MM-DD/` 创建新目录
- 创建 `page.tsx` 文件
- 使用提供的组件库来构建文章内容

### 3. 组件库使用

#### 可用组件

```typescript
import { 
  ImageContainer, 
  HighlightBox, 
  CodeBlock, 
  Section, 
  Divider 
} from '../components';
```

#### ImageContainer
```tsx
<ImageContainer 
  src="/posts/YYYY-MM-DD/img/image.png" 
  alt="图片描述" 
  caption="图片说明（可选）"
  width="100%"
  height="auto"
/>
```

#### HighlightBox
```tsx
<HighlightBox type="info" title="标题（可选）">
  <p>内容可以包含任何 JSX 元素</p>
</HighlightBox>
```

支持的类型：`info`, `warning`, `success`, `error`

#### CodeBlock
```tsx
<CodeBlock language="javascript" title="代码标题（可选）">
  {`// 代码内容
function example() {
  console.log('Hello World');
}`}
</CodeBlock>
```

#### Section 和 Divider
```tsx
<Section>
  <h2>章节内容</h2>
  <p>段落内容...</p>
</Section>

<Divider />
```

### 4. 文章模板

```tsx
'use client';

import React from 'react';
import ArticleLayout, { ArticleMetadata } from '@/components/layout/ArticleLayout';
import { ImageContainer, HighlightBox, CodeBlock, Section, Divider } from '../components';

export default function ArticlePage() {
  const metadata: ArticleMetadata = {
    title: '文章标题',
    date: 'YYYY-MM-DD',
    excerpt: '文章摘要',
  };

  return (
    <ArticleLayout metadata={metadata}>
      <div className="article-content">
        {/* 文章内容 */}
      </div>
    </ArticleLayout>
  );
}
```

## 优势

1. **类型安全**：完整的 TypeScript 支持
2. **组件复用**：统一的样式和行为
3. **开发体验**：代码提示和错误检查
4. **维护性**：组件化的架构便于维护
5. **兼容性**：与现有的微信公众号样式完全兼容

## 最佳实践

1. **保持一致性**：使用统一的组件而不是自定义 HTML
2. **图片管理**：将图片放在 `public/posts/YYYY-MM-DD/img/` 目录下
3. **代码组织**：将复杂的内容拆分为多个 Section 组件
4. **样式继承**：依赖 ArticleLayout 提供的基础样式

## 未来规划

- 探索自动化工具，减少从 Markdown 到 TSX 的转换工作
- 监控 MDX 技术栈的成熟度，为未来迁移做准备
- 继续优化组件库，添加更多实用组件

## 故障排除

### 常见问题

1. **组件导入错误**
   ```typescript
   // 正确的导入方式
   import { HighlightBox } from '../components';
   ```

2. **图片路径问题**
   ```typescript
   // 确保图片路径以 /posts/ 开头
   src="/posts/2025-01-15/img/image.png"
   ```

3. **TypeScript 错误**
   ```typescript
   // 确保 metadata 对象包含必需的字段
   const metadata: ArticleMetadata = {
     title: '标题',
     date: '2025-01-15',
     // excerpt 是可选的
   };
   ```
