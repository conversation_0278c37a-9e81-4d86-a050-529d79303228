# 快速开始指南 - 微信公众号文章工作流程优化

## 概述

本指南帮助您快速启动微信公众号文章工作流程优化项目。按照以下步骤，您可以在1-2小时内搭建基础的富文本编辑系统。

## 前置条件

- Node.js 18+ 已安装
- pnpm 包管理器已安装
- 项目已在本地运行

## 第一步：安装核心依赖（5分钟）

```bash
# 进入项目目录
cd /Users/<USER>/WorkingPlace/Writing/SiliconBasedTeahouse2077

# 安装TipTap核心包
pnpm add @tiptap/react @tiptap/starter-kit @tiptap/pm @tiptap/extension-image

# 安装数据库相关（简化版，先用内存存储）
pnpm add zustand

# 安装表单处理
pnpm add react-hook-form zod
```

## 第二步：创建基础目录结构（2分钟）

```bash
# 创建必要的目录
mkdir -p src/components/editor
mkdir -p src/app/admin/articles
mkdir -p src/stores
mkdir -p src/types
```

## 第三步：创建基础类型定义（3分钟）

```typescript
// src/types/article.ts
export interface ArticleData {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  content: any; // TipTap JSON
  status: 'draft' | 'published';
  createdAt: Date;
  updatedAt: Date;
}

export interface ArticleFormData {
  slug: string;
  title: string;
  excerpt?: string;
  content: any;
  status: 'draft' | 'published';
}
```

## 第四步：创建简单的状态管理（5分钟）

```typescript
// src/stores/article-store.ts
import { create } from 'zustand';
import { ArticleData, ArticleFormData } from '@/types/article';

interface ArticleStore {
  articles: ArticleData[];
  currentArticle: ArticleData | null;
  
  // Actions
  addArticle: (data: ArticleFormData) => void;
  updateArticle: (id: string, data: Partial<ArticleFormData>) => void;
  deleteArticle: (id: string) => void;
  getArticleBySlug: (slug: string) => ArticleData | undefined;
  setCurrentArticle: (article: ArticleData | null) => void;
}

export const useArticleStore = create<ArticleStore>((set, get) => ({
  articles: [],
  currentArticle: null,

  addArticle: (data) => {
    const newArticle: ArticleData = {
      id: Date.now().toString(),
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    set((state) => ({
      articles: [newArticle, ...state.articles],
    }));
  },

  updateArticle: (id, data) => {
    set((state) => ({
      articles: state.articles.map((article) =>
        article.id === id
          ? { ...article, ...data, updatedAt: new Date() }
          : article
      ),
    }));
  },

  deleteArticle: (id) => {
    set((state) => ({
      articles: state.articles.filter((article) => article.id !== id),
    }));
  },

  getArticleBySlug: (slug) => {
    return get().articles.find((article) => article.slug === slug);
  },

  setCurrentArticle: (article) => {
    set({ currentArticle: article });
  },
}));
```

## 第五步：创建简化的TipTap编辑器（10分钟）

```typescript
// src/components/editor/SimpleTipTapEditor.tsx
'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { Button } from '@/components/ui/button';
import { Bold, Italic, List, ListOrdered, Quote, Image as ImageIcon } from 'lucide-react';

interface SimpleTipTapEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  editable?: boolean;
}

export default function SimpleTipTapEditor({
  content,
  onChange,
  editable = true,
}: SimpleTipTapEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[300px] p-4 border-t',
      },
    },
  });

  if (!editor) {
    return <div className="animate-pulse bg-gray-200 h-64 rounded"></div>;
  }

  const addImage = () => {
    const url = window.prompt('请输入图片URL:');
    if (url) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  };

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
      {editable && (
        <div className="border-b border-gray-300 p-2 flex gap-1 flex-wrap">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive('bold') ? 'bg-gray-200' : ''}
          >
            <Bold className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'bg-gray-200' : ''}
          >
            <Italic className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
          >
            <List className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}
          >
            <ListOrdered className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}
          >
            <Quote className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={addImage}
          >
            <ImageIcon className="h-4 w-4" />
          </Button>

          <div className="border-l border-gray-300 mx-2"></div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}
          >
            H2
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            className={editor.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}
          >
            H3
          </Button>
        </div>
      )}
      
      <EditorContent editor={editor} />
    </div>
  );
}
```

## 第六步：创建文章编辑页面（15分钟）

```typescript
// src/app/admin/articles/page.tsx
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useArticleStore } from '@/stores/article-store';
import { Plus, Edit, Eye, Trash2 } from 'lucide-react';

export default function ArticlesAdminPage() {
  const { articles, deleteArticle } = useArticleStore();

  const handleDelete = (id: string) => {
    if (confirm('确定要删除这篇文章吗？')) {
      deleteArticle(id);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
          文章管理
        </h1>
        <Link href="/admin/articles/new">
          <Button className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600">
            <Plus className="h-4 w-4 mr-2" />
            新建文章
          </Button>
        </Link>
      </div>

      {articles.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">还没有文章，开始创建第一篇吧！</p>
          <Link href="/admin/articles/new">
            <Button>创建文章</Button>
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  标题
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {articles.map((article) => (
                <tr key={article.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {article.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      {article.slug}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      article.status === 'published' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {article.status === 'published' ? '已发布' : '草稿'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {article.createdAt.toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Link href={`/posts/${article.slug}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/admin/articles/edit/${article.id}`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDelete(article.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
```

## 第七步：创建新建文章页面（15分钟）

```typescript
// src/app/admin/articles/new/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import SimpleTipTapEditor from '@/components/editor/SimpleTipTapEditor';
import { useArticleStore } from '@/stores/article-store';

export default function NewArticlePage() {
  const router = useRouter();
  const { addArticle } = useArticleStore();
  
  const [formData, setFormData] = useState({
    slug: '',
    title: '',
    excerpt: '',
    content: null,
    status: 'draft' as const,
  });

  const [isPreview, setIsPreview] = useState(false);

  const generateSlug = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  const handleSave = () => {
    if (!formData.title || !formData.slug) {
      alert('请填写标题和路径');
      return;
    }

    addArticle(formData);
    router.push('/admin/articles');
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(),
    }));
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400">
          新建文章
        </h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsPreview(!isPreview)}
          >
            {isPreview ? '编辑' : '预览'}
          </Button>
          <Button onClick={handleSave}>
            保存文章
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 编辑区域 */}
        <div className={isPreview ? 'hidden lg:block' : ''}>
          <div className="space-y-4 mb-6 bg-white p-6 rounded-lg shadow">
            <div>
              <Label htmlFor="title">文章标题 *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleTitleChange(e.target.value)}
                placeholder="输入文章标题"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="slug">文章路径 *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="YYYY-MM-DD"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="excerpt">文章摘要</Label>
              <Textarea
                id="excerpt"
                value={formData.excerpt}
                onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                placeholder="输入文章摘要（可选）"
                rows={3}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label className="text-lg font-medium">文章内容</Label>
            <div className="mt-2">
              <SimpleTipTapEditor
                content={formData.content}
                onChange={(content) => setFormData(prev => ({ ...prev, content }))}
              />
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className={!isPreview ? 'hidden lg:block' : ''}>
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-4">预览</h2>
            <div className="border rounded-lg p-4">
              <h1 className="text-2xl font-bold mb-2">{formData.title || '文章标题'}</h1>
              {formData.excerpt && (
                <p className="text-gray-600 mb-4">{formData.excerpt}</p>
              )}
              <div className="prose prose-lg max-w-none">
                {formData.content ? (
                  <SimpleTipTapEditor
                    content={formData.content}
                    editable={false}
                  />
                ) : (
                  <p className="text-gray-400">开始编写内容...</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 第八步：添加导航链接（5分钟）

在您的主导航中添加管理链接：

```typescript
// 在 src/components/layout/Header.tsx 或相应的导航组件中添加
<Link href="/admin/articles" className="nav-link">
  文章管理
</Link>
```

## 第九步：测试运行（5分钟）

```bash
# 启动开发服务器
pnpm dev

# 访问以下页面测试：
# http://localhost:3002/admin/articles - 文章列表
# http://localhost:3002/admin/articles/new - 新建文章
```

## 完成！

现在您已经有了一个基础的富文本编辑系统：

✅ **已实现的功能：**
- 基础的TipTap富文本编辑器
- 文章创建和管理界面
- 实时预览功能
- 简单的状态管理

🚀 **下一步优化：**
1. 添加WeChat组件扩展
2. 集成数据库持久化
3. 添加图片上传功能
4. 实现Markdown导入
5. 完善样式和用户体验

**预计时间：** 总共约1小时完成基础版本

**注意事项：**
- 当前使用内存存储，刷新页面数据会丢失
- 可以先用这个版本测试工作流程
- 后续按照完整实施指南添加更多功能
